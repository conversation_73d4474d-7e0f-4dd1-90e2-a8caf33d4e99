<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Builder - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        @media print {
            /* Hide everything by default */
            body * {
                visibility: hidden;
            }

            /* Show only the resume preview */
            #resume-preview,
            #resume-preview * {
                visibility: visible;
            }

            /* Remove all margins and padding from body */
            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
            }

            /* Style the resume for clean printing */
            #resume-preview {
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                transform: none !important;
                box-shadow: none !important;
                border: none !important;
                border-radius: 0 !important;
                margin: 0 !important;
                padding: 15mm !important;
                background: white !important;
                font-family: Arial, sans-serif !important;
                font-size: 11px !important;
                line-height: 1.3 !important;
                color: black !important;
            }

            /* Ensure parent containers don't interfere */
            .lg\:w-1\/2 {
                width: 100% !important;
                position: static !important;
            }

            /* Hide all other elements completely */
            nav, header, .sticky, .shadow-md, .bg-white.rounded-lg.shadow-md,
            .lg\:w-1\/2:first-child, .max-w-7xl, .grid, .px-4, .py-12 {
                display: none !important;
            }

            /* Adjust text sizes for print */
            #resume-preview h1 {
                font-size: 16px !important;
                margin-bottom: 4px !important;
            }
            #resume-preview h2 {
                font-size: 13px !important;
                margin-bottom: 6px !important;
                margin-top: 12px !important;
            }
            #resume-preview h3 {
                font-size: 11px !important;
                margin-bottom: 2px !important;
            }
            #resume-preview p,
            #resume-preview span,
            #resume-preview li {
                font-size: 10px !important;
                margin-bottom: 2px !important;
            }

            /* Ensure proper spacing */
            #resume-preview .mb-6 {
                margin-bottom: 12px !important;
            }
            #resume-preview .mb-4 {
                margin-bottom: 8px !important;
            }
            #resume-preview .mb-3 {
                margin-bottom: 6px !important;
            }
            #resume-preview .mb-2 {
                margin-bottom: 4px !important;
            }
            #resume-preview .mb-1 {
                margin-bottom: 2px !important;
            }
        }

        /* Additional styles for better preview */
        .resume-preview {
            font-family: 'Inter', sans-serif;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-primary font-medium">Resume Builder</a>
                    <!-- For Employers dropdown removed for security -->
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold mb-4">Professional Resume Builder</h1>
            <p class="text-xl text-blue-100 mb-6">Create a stunning resume in minutes with our easy-to-use builder</p>
            <div class="flex items-center justify-center space-x-8 text-blue-100">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>ATS-Friendly Templates</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Professional Designs</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Easy Export</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Resume Builder -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Form Section -->
            <div class="lg:w-1/2">
                <!-- Progress Bar -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-gray-900">Resume Progress</h3>
                        <span class="text-sm text-gray-600" id="progress-text">0% Complete</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Resume Form -->
                <div class="bg-white rounded-lg shadow-md">
                    <!-- Form Tabs -->
                    <div class="border-b">
                        <nav class="flex space-x-8 px-6">
                            <button class="tab-btn py-4 border-b-2 border-primary text-primary font-medium" data-tab="personal">
                                <i class="fas fa-user mr-2"></i>Personal Info
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="experience">
                                <i class="fas fa-briefcase mr-2"></i>Experience
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="education">
                                <i class="fas fa-graduation-cap mr-2"></i>Education
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="skills">
                                <i class="fas fa-cogs mr-2"></i>Skills
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Personal Info Tab -->
                        <div id="personal" class="tab-content">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                            <form class="space-y-4">
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="John">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Doe">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Professional Title</label>
                                    <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Senior Software Engineer">
                                </div>
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                        <input type="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="<EMAIL>">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                        <input type="tel" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="+****************">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="San Francisco, CA">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">LinkedIn Profile</label>
                                    <input type="url" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://linkedin.com/in/johndoe">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                                    <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Write a brief summary of your professional background and key achievements..."></textarea>
                                </div>
                            </form>
                        </div>

                        <!-- Experience Tab -->
                        <div id="experience" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Work Experience</h3>
                                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition text-sm" id="add-experience-btn">
                                    <i class="fas fa-plus mr-2"></i>Add Experience
                                </button>
                            </div>
                            
                            <!-- Experience Entry -->
                            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Experience #1</h4>
                                    <button class="text-red-500 hover:text-red-700 remove-experience-btn">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <form class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Senior Software Engineer">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Google Inc.">
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                            <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                            <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <label class="flex items-center mt-2">
                                                <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                                <span class="ml-2 text-sm text-gray-700">Currently working here</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Mountain View, CA">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Description</label>
                                        <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Describe your responsibilities and achievements..."></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Education Tab -->
                        <div id="education" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Education</h3>
                                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition text-sm" id="add-education-btn">
                                    <i class="fas fa-plus mr-2"></i>Add Education
                                </button>
                            </div>
                            
                            <!-- Education Entry -->
                            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Education #1</h4>
                                    <button class="text-red-500 hover:text-red-700 remove-education-btn">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <form class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Degree</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Bachelor of Science in Computer Science">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">School/University</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Stanford University">
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Year</label>
                                            <input type="number" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="2018">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">End Year</label>
                                            <input type="number" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="2022">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">GPA (Optional)</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="3.8/4.0">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Relevant Coursework (Optional)</label>
                                        <textarea rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Data Structures, Algorithms, Software Engineering..."></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Skills Tab -->
                        <div id="skills" class="tab-content hidden">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Skills & Technologies</h3>
                            <form class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Technical Skills</label>
                                    <div class="flex flex-wrap gap-2 mb-3" id="technical-skills">
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            JavaScript <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            React <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            Node.js <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                    </div>
                                    <div class="flex">
                                        <input type="text" id="skill-input" class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Add a skill">
                                        <button type="button" id="add-skill-btn" class="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-secondary transition">Add</button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Languages</label>
                                    <div class="space-y-3" id="languages-container">
                                        <div class="flex items-center space-x-4">
                                            <input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Language" value="English">
                                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                                <option>Native</option>
                                                <option>Fluent</option>
                                                <option>Intermediate</option>
                                                <option>Basic</option>
                                            </select>
                                            <button type="button" class="text-red-500 hover:text-red-700 remove-language-btn">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <button type="button" class="text-primary hover:underline text-sm" id="add-language-btn">
                                            <i class="fas fa-plus mr-1"></i>Add Language
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Certifications (Optional)</label>
                                    <div class="space-y-3" id="certifications-container">
                                        <div class="flex items-center space-x-2">
                                            <input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Certification Name">
                                            <button type="button" class="text-red-500 hover:text-red-700 remove-certification-btn">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <button type="button" class="text-primary hover:underline text-sm" id="add-certification-btn">
                                            <i class="fas fa-plus mr-1"></i>Add Certification
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="border-t px-6 py-4">
                        <div class="flex items-center justify-between">
                            <button class="text-gray-600 hover:text-gray-800 transition" id="save-draft-btn">
                                <i class="fas fa-save mr-2"></i>Save Draft
                            </button>
                            <div class="flex items-center space-x-3">
                                <!-- Previous Button (hidden on first tab) -->
                                <button class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition hidden" id="prev-btn">
                                    <i class="fas fa-arrow-left mr-2"></i>Previous
                                </button>
                                <!-- Next Button (shown on first 3 tabs) -->
                                <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition" id="next-btn">
                                    Next<i class="fas fa-arrow-right ml-2"></i>
                                </button>
                                <!-- Final Actions (shown only on last tab) -->
                                <div class="hidden space-x-3" id="final-actions">
                                    <button class="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition" id="preview-btn" onclick="updatePreview()">
                                        <i class="fas fa-eye mr-2"></i>Preview
                                    </button>
                                    <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition" onclick="generateTextPDF()">
                                        <i class="fas fa-download mr-2"></i>Download PDF
                                    </button>
                                    <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition text-sm" onclick="generatePDF()">
                                        <i class="fas fa-image mr-2"></i>Image PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="lg:w-1/2">
                <div class="sticky top-24">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-900">Resume Preview</h3>
                            <div class="flex space-x-2">
                                <button class="p-2 text-gray-600 hover:text-gray-800 transition" title="Zoom Out">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="p-2 text-gray-600 hover:text-gray-800 transition" title="Zoom In">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Resume Preview -->
                        <div class="border border-gray-300 rounded-lg p-6 bg-white resume-preview" id="resume-preview" style="min-height: 600px; transform: scale(0.8); transform-origin: top left;">
                            <!-- Resume Header -->
                            <div class="text-center mb-6">
                                <h1 class="text-2xl font-bold text-gray-900 mb-1">John Doe</h1>
                                <p class="text-lg text-gray-600 mb-2">Senior Software Engineer</p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-600">
                                    <span><i class="fas fa-envelope mr-1"></i><EMAIL></span>
                                    <span><i class="fas fa-phone mr-1"></i>+****************</span>
                                    <span><i class="fas fa-map-marker-alt mr-1"></i>San Francisco, CA</span>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Professional Summary</h2>
                                <p class="text-gray-700 text-sm leading-relaxed">
                                    Experienced software engineer with 5+ years of expertise in full-stack development, 
                                    specializing in React, Node.js, and cloud technologies. Proven track record of 
                                    delivering scalable solutions and leading cross-functional teams.
                                </p>
                            </div>

                            <!-- Experience -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Experience</h2>
                                <div class="mb-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">Senior Software Engineer</h3>
                                        <span class="text-sm text-gray-600">2021 - Present</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Google Inc. • Mountain View, CA</p>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Led development of key features serving 10M+ users</li>
                                        <li>• Improved application performance by 40% through optimization</li>
                                        <li>• Mentored junior developers and conducted code reviews</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Education -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Education</h2>
                                <div>
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">Bachelor of Science in Computer Science</h3>
                                        <span class="text-sm text-gray-600">2018 - 2022</span>
                                    </div>
                                    <p class="text-sm text-gray-600">Stanford University • GPA: 3.8/4.0</p>
                                </div>
                            </div>

                            <!-- Skills -->
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Skills</h2>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">JavaScript</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">React</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">Node.js</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">Python</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">AWS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <!-- <li><a href="login.html" class="hover:text-white transition">Sign In</a></li> -->
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs with custom navigation
            initializeResumeBuilder();

            // Skill management
            const skillInput = document.getElementById('skill-input');
            const addSkillBtn = document.getElementById('add-skill-btn');
            const skillsContainer = document.getElementById('technical-skills');
            
            function addSkill() {
                const skillText = skillInput.value.trim();
                if (skillText) {
                    const skillTag = document.createElement('span');
                    skillTag.className = 'bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center';
                    skillTag.innerHTML = `${skillText} <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>`;
                    
                    skillTag.querySelector('button').addEventListener('click', function() {
                        skillTag.remove();
                        updatePreview(); // Update preview when skill is removed
                    });

                    skillsContainer.appendChild(skillTag);
                    skillInput.value = '';
                    updatePreview(); // Update preview when skill is added
                }
            }
            
            addSkillBtn.addEventListener('click', addSkill);
            skillInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addSkill();
                }
            });
            
            // Remove skill functionality for existing skills
            document.querySelectorAll('#technical-skills button').forEach(button => {
                button.addEventListener('click', function() {
                    this.parentElement.remove();
                    updatePreview(); // Update preview when existing skill is removed
                });
            });

            // Language functionality
            let languageCount = 1;
            document.getElementById('add-language-btn').addEventListener('click', function() {
                languageCount++;
                const languagesContainer = document.getElementById('languages-container');
                const newLanguage = createLanguageEntry();

                // Insert before the add button
                languagesContainer.insertBefore(newLanguage, this);

                // Add event listeners to new inputs
                const newInputs = newLanguage.querySelectorAll('input, select');
                newInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                    input.addEventListener('change', updatePreview);
                });

                // EasyNaukri.showNotification('Language entry added!', 'success');
                updatePreview();
            });

            // Certification functionality
            let certificationCount = 1;
            document.getElementById('add-certification-btn').addEventListener('click', function() {
                certificationCount++;
                const certificationsContainer = document.getElementById('certifications-container');
                const newCertification = createCertificationEntry();

                // Insert before the add button
                certificationsContainer.insertBefore(newCertification, this);

                // Add event listeners to new inputs
                const newInputs = newCertification.querySelectorAll('input');
                newInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                });

                // EasyNaukri.showNotification('Certification entry added!', 'success');
                updatePreview();
            });

            // Remove language/certification functionality
            document.addEventListener('click', function(e) {
                if (e.target.closest('.remove-language-btn')) {
                    const languageEntry = e.target.closest('.flex.items-center');
                    languageEntry.remove();
                    // EasyNaukri.showNotification('Language entry removed!', 'info');
                    updatePreview();
                }

                if (e.target.closest('.remove-certification-btn')) {
                    const certificationEntry = e.target.closest('.flex.items-center');
                    certificationEntry.remove();
                    // EasyNaukri.showNotification('Certification entry removed!', 'info');
                    updatePreview();
                }
            });

            // Add Experience functionality
            let experienceCount = 1;
            document.getElementById('add-experience-btn').addEventListener('click', function() {
                experienceCount++;
                const experienceContainer = document.querySelector('#experience');
                const newExperience = createExperienceEntry(experienceCount);

                // Insert before the add button
                const addButton = this.parentElement;
                addButton.parentElement.insertBefore(newExperience, addButton);

                // Add event listeners to new inputs
                const newInputs = newExperience.querySelectorAll('input, textarea');
                newInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                });

                // EasyNaukri.showNotification('Experience entry added!', 'success');
                updatePreview(); // Update preview immediately
            });

            // Add Education functionality
            let educationCount = 1;
            document.getElementById('add-education-btn').addEventListener('click', function() {
                educationCount++;
                const educationContainer = document.querySelector('#education');
                const newEducation = createEducationEntry(educationCount);

                // Insert before the add button
                const addButton = this.parentElement;
                addButton.parentElement.insertBefore(newEducation, addButton);

                // Add event listeners to new inputs
                const newInputs = newEducation.querySelectorAll('input, textarea');
                newInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                });

                // EasyNaukri.showNotification('Education entry added!', 'success');
                updatePreview(); // Update preview immediately
            });

            // Remove Experience/Education functionality
            document.addEventListener('click', function(e) {
                if (e.target.closest('.remove-experience-btn')) {
                    const experienceEntry = e.target.closest('.border');
                    experienceEntry.remove();
                    // EasyNaukri.showNotification('Experience entry removed!', 'info');
                    updatePreview(); // Update preview after removal
                }

                if (e.target.closest('.remove-education-btn')) {
                    const educationEntry = e.target.closest('.border');
                    educationEntry.remove();
                    // EasyNaukri.showNotification('Education entry removed!', 'info');
                    updatePreview(); // Update preview after removal
                }
            });

            // Helper function to create new experience entry
            function createExperienceEntry(count) {
                const div = document.createElement('div');
                div.className = 'border border-gray-200 rounded-lg p-4 mb-4';
                div.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Experience #${count}</h4>
                        <button class="text-red-500 hover:text-red-700 remove-experience-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Senior Software Engineer">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Tech Company Inc.">
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Describe your responsibilities and achievements..."></textarea>
                        </div>
                    </form>
                `;
                return div;
            }

            // Helper function to create new education entry
            function createEducationEntry(count) {
                const div = document.createElement('div');
                div.className = 'border border-gray-200 rounded-lg p-4 mb-4';
                div.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Education #${count}</h4>
                        <button class="text-red-500 hover:text-red-700 remove-education-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Degree</label>
                            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Bachelor of Science in Computer Science">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">School/University</label>
                            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="University of Technology">
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">GPA (Optional)</label>
                            <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="3.8/4.0">
                        </div>
                    </form>
                `;
                return div;
            }

            // Helper function to create new language entry
            function createLanguageEntry() {
                const div = document.createElement('div');
                div.className = 'flex items-center space-x-4';
                div.innerHTML = `
                    <input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Language">
                    <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>Native</option>
                        <option>Fluent</option>
                        <option>Intermediate</option>
                        <option>Basic</option>
                    </select>
                    <button type="button" class="text-red-500 hover:text-red-700 remove-language-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                return div;
            }

            // Helper function to create new certification entry
            function createCertificationEntry() {
                const div = document.createElement('div');
                div.className = 'flex items-center space-x-2';
                div.innerHTML = `
                    <input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Certification Name">
                    <button type="button" class="text-red-500 hover:text-red-700 remove-certification-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                return div;
            }

            // Save draft functionality
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                const icon = button.querySelector('i');
                if (icon && icon.classList.contains('fa-save')) {
                    button.addEventListener('click', function() {
                        // EasyNaukri.showNotification('Resume draft saved successfully!', 'success');
                    });
                } else if (icon && icon.classList.contains('fa-download')) {
                    button.addEventListener('click', function() {
                        generatePDF();
                    });
                } else if (icon && icon.classList.contains('fa-eye')) {
                    button.addEventListener('click', function() {
                        updatePreview();
                        // EasyNaukri.showNotification('Preview updated!', 'success');
                    });
                }
            });

            // Real-time preview updates for ALL sections
            function setupPreviewListeners() {
                // Personal info listeners
                const personalInputs = document.querySelectorAll('#personal input, #personal textarea');
                personalInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                });

                // Experience listeners (for existing and new entries)
                document.addEventListener('input', function(e) {
                    if (e.target.closest('#experience')) {
                        updatePreview();
                    }
                });

                // Education listeners (for existing and new entries)
                document.addEventListener('input', function(e) {
                    if (e.target.closest('#education')) {
                        updatePreview();
                    }
                });

                // Skills listeners
                document.addEventListener('input', function(e) {
                    if (e.target.closest('#skills')) {
                        updatePreview();
                    }
                });

                // Languages listeners
                document.addEventListener('input', function(e) {
                    if (e.target.closest('#languages-container')) {
                        updatePreview();
                    }
                });

                document.addEventListener('change', function(e) {
                    if (e.target.closest('#languages-container')) {
                        updatePreview();
                    }
                });

                // Certifications listeners
                document.addEventListener('input', function(e) {
                    if (e.target.closest('#certifications-container')) {
                        updatePreview();
                    }
                });

                console.log('Preview listeners setup complete');
            }

            // Initialize preview listeners
            setupPreviewListeners();

            function updatePreview() {
                try {
                    console.log('Updating complete preview...');

                    // Find preview container
                    const previewContainer = document.getElementById('resume-preview');
                    if (!previewContainer) {
                        console.error('Preview container not found');
                        EasyNaukri.showNotification('Preview container not found!', 'error');
                        return;
                    }

                    // === PERSONAL INFORMATION ===
                    const firstName = document.querySelector('#personal input[placeholder="John"]')?.value || 'John';
                    const lastName = document.querySelector('#personal input[placeholder="Doe"]')?.value || 'Doe';
                    const title = document.querySelector('#personal input[placeholder="Senior Software Engineer"]')?.value || 'Senior Software Engineer';
                    const email = document.querySelector('#personal input[type="email"]')?.value || '<EMAIL>';
                    const phone = document.querySelector('#personal input[type="tel"]')?.value || '+****************';
                    const location = document.querySelector('#personal input[placeholder="San Francisco, CA"]')?.value || 'San Francisco, CA';
                    const linkedin = document.querySelector('#personal input[type="url"]')?.value || '';
                    const summary = document.querySelector('#personal textarea')?.value || 'Experienced professional with expertise in various technologies.';

                    console.log('Personal data:', { firstName, lastName, title, email, phone, location });

                    // Update header
                    const nameElement = previewContainer.querySelector('h1');
                    if (nameElement) nameElement.textContent = `${firstName} ${lastName}`;

                    const titleElement = previewContainer.querySelector('p.text-lg');
                    if (titleElement) titleElement.textContent = title;

                    // Update contact info
                    const contactElements = previewContainer.querySelectorAll('.text-center .flex span');
                    if (contactElements.length >= 3) {
                        contactElements[0].innerHTML = `<i class="fas fa-envelope mr-1"></i>${email}`;
                        contactElements[1].innerHTML = `<i class="fas fa-phone mr-1"></i>${phone}`;
                        contactElements[2].innerHTML = `<i class="fas fa-map-marker-alt mr-1"></i>${location}`;
                    }

                    // Update summary
                    const summaryElement = previewContainer.querySelector('.mb-6 p');
                    if (summaryElement) summaryElement.textContent = summary;

                    // === EXPERIENCE SECTION ===
                    updateExperiencePreview(previewContainer);

                    // === EDUCATION SECTION ===
                    updateEducationPreview(previewContainer);

                    // === SKILLS SECTION ===
                    updateSkillsPreview(previewContainer);

                    // === LANGUAGES SECTION ===
                    updateLanguagesPreview(previewContainer);

                    // === CERTIFICATIONS SECTION ===
                    updateCertificationsPreview(previewContainer);

                    console.log('Complete preview updated successfully');
                    // EasyNaukri.showNotification('Preview updated with all your information!', 'success');

                    // Scroll to preview on mobile
                    if (window.innerWidth < 1024) {
                        previewContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }

                } catch (error) {
                    console.error('Error updating preview:', error);
                    EasyNaukri.showNotification('Error updating preview: ' + error.message, 'error');
                }
            }

            // Update Experience Section in Preview
            function updateExperiencePreview(previewContainer) {
                try {
                    // Find experience section by looking for the h2 with "Experience" text
                    const experienceSections = previewContainer.querySelectorAll('.mb-6');
                    let experienceSection = null;

                    experienceSections.forEach(section => {
                        const h2 = section.querySelector('h2');
                        if (h2 && h2.textContent.includes('Experience')) {
                            experienceSection = section;
                        }
                    });

                    if (!experienceSection) {
                        console.log('Experience section not found in preview');
                        return;
                    }

                    // Get all experience entries
                    const experienceEntries = document.querySelectorAll('#experience .border.border-gray-200');
                    let experienceHTML = '<h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Experience</h2>';

                    if (experienceEntries.length === 0) {
                        experienceHTML += '<p class="text-sm text-gray-600">No experience added yet.</p>';
                    } else {
                        experienceEntries.forEach((entry, index) => {
                            const jobTitle = entry.querySelector('input[placeholder*="Senior Software Engineer"]')?.value || 'Job Title';
                            const company = entry.querySelector('input[placeholder*="Google Inc"]')?.value || 'Company Name';
                            const startDate = entry.querySelector('input[type="month"]:first-of-type')?.value || '';
                            const endDate = entry.querySelector('input[type="month"]:last-of-type')?.value || '';
                            const isCurrentJob = entry.querySelector('input[type="checkbox"]')?.checked || false;
                            const description = entry.querySelector('textarea')?.value || 'Job description not provided.';

                            // Format dates properly
                            const formatDate = (dateStr) => {
                                if (!dateStr) return '';
                                const [year, month] = dateStr.split('-');
                                const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                return `${monthNames[parseInt(month) - 1]} ${year}`;
                            };

                            const formattedStartDate = formatDate(startDate);
                            const formattedEndDate = isCurrentJob ? 'Present' : formatDate(endDate);
                            const dateRange = formattedStartDate ? `${formattedStartDate} - ${formattedEndDate}` : 'Date Range';

                            experienceHTML += `
                                <div class="mb-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">${jobTitle}</h3>
                                        <span class="text-sm text-gray-600">${dateRange}</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">${company}</p>
                                    <p class="text-sm text-gray-700">${description}</p>
                                </div>
                            `;
                        });
                    }

                    experienceSection.innerHTML = experienceHTML;
                    console.log('Experience section updated');
                } catch (error) {
                    console.error('Error updating experience preview:', error);
                }
            }

            // Update Education Section in Preview
            function updateEducationPreview(previewContainer) {
                try {
                    // Find education section by looking for the h2 with "Education" text
                    const educationSections = previewContainer.querySelectorAll('.mb-6');
                    let educationSection = null;

                    educationSections.forEach(section => {
                        const h2 = section.querySelector('h2');
                        if (h2 && h2.textContent.includes('Education')) {
                            educationSection = section;
                        }
                    });

                    if (!educationSection) {
                        console.log('Education section not found in preview');
                        return;
                    }

                    // Get all education entries
                    const educationEntries = document.querySelectorAll('#education .border.border-gray-200');
                    let educationHTML = '<h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Education</h2>';

                    if (educationEntries.length === 0) {
                        educationHTML += '<p class="text-sm text-gray-600">No education added yet.</p>';
                    } else {
                        educationEntries.forEach((entry, index) => {
                            const degree = entry.querySelector('input[placeholder*="Bachelor"]')?.value || 'Degree';
                            const school = entry.querySelector('input[placeholder*="University"]')?.value || 'School/University';
                            const startDate = entry.querySelector('input[type="month"]:first-of-type')?.value || '';
                            const endDate = entry.querySelector('input[type="month"]:last-of-type')?.value || '';
                            const gpa = entry.querySelector('input[placeholder*="GPA"]')?.value || '';

                            const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : 'Date Range';

                            educationHTML += `
                                <div class="mb-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">${degree}</h3>
                                        <span class="text-sm text-gray-600">${dateRange}</span>
                                    </div>
                                    <p class="text-sm text-gray-600">${school}</p>
                                    ${gpa ? `<p class="text-sm text-gray-600">GPA: ${gpa}</p>` : ''}
                                </div>
                            `;
                        });
                    }

                    educationSection.innerHTML = educationHTML;
                    console.log('Education section updated');
                } catch (error) {
                    console.error('Error updating education preview:', error);
                }
            }

            // Update Skills Section in Preview
            function updateSkillsPreview(previewContainer) {
                try {
                    // Find skills section - it's the last div without mb-6 class
                    let skillsSection = null;
                    const allSections = previewContainer.querySelectorAll('div');

                    // Look for the section with Skills h2
                    allSections.forEach(section => {
                        const h2 = section.querySelector('h2');
                        if (h2 && h2.textContent.includes('Skills')) {
                            skillsSection = section;
                        }
                    });

                    if (!skillsSection) {
                        console.log('Skills section not found in preview');
                        return;
                    }

                    // Get skills from the skills section
                    const skillTags = document.querySelectorAll('#technical-skills .bg-blue-100');
                    let skillsHTML = '<h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Skills</h2>';

                    if (skillTags.length === 0) {
                        skillsHTML += '<p class="text-sm text-gray-600">No skills added yet.</p>';
                    } else {
                        skillsHTML += '<div class="flex flex-wrap gap-2">';
                        skillTags.forEach(tag => {
                            const skillText = tag.textContent.replace('×', '').trim();
                            if (skillText) {
                                skillsHTML += `<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${skillText}</span>`;
                            }
                        });
                        skillsHTML += '</div>';
                    }

                    skillsSection.innerHTML = skillsHTML;
                    console.log('Skills section updated');
                } catch (error) {
                    console.error('Error updating skills preview:', error);
                }
            }

            // Update Languages Section in Preview
            function updateLanguagesPreview(previewContainer) {
                try {
                    // Find or create languages section in preview
                    let languagesSection = null;
                    const allSections = previewContainer.querySelectorAll('div');

                    allSections.forEach(section => {
                        const h2 = section.querySelector('h2');
                        if (h2 && h2.textContent.includes('Languages')) {
                            languagesSection = section;
                        }
                    });

                    // If no languages section exists, create one after skills
                    if (!languagesSection) {
                        languagesSection = document.createElement('div');
                        languagesSection.className = 'mb-6';

                        // Find skills section and insert after it
                        const skillsSection = Array.from(previewContainer.querySelectorAll('div')).find(section => {
                            const h2 = section.querySelector('h2');
                            return h2 && h2.textContent.includes('Skills');
                        });

                        if (skillsSection) {
                            skillsSection.parentNode.insertBefore(languagesSection, skillsSection.nextSibling);
                        } else {
                            previewContainer.appendChild(languagesSection);
                        }
                    }

                    // Get language entries
                    const languageEntries = document.querySelectorAll('#languages-container .flex.items-center.space-x-4');
                    let languagesHTML = '<h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Languages</h2>';

                    if (languageEntries.length === 0) {
                        languagesHTML += '<p class="text-sm text-gray-600">No languages added yet.</p>';
                    } else {
                        languagesHTML += '<div class="grid grid-cols-2 gap-2">';
                        languageEntries.forEach(entry => {
                            const language = entry.querySelector('input')?.value || 'Language';
                            const proficiency = entry.querySelector('select')?.value || 'Basic';
                            if (language && language !== 'Language') {
                                languagesHTML += `<div class="text-sm"><span class="font-medium">${language}</span> - ${proficiency}</div>`;
                            }
                        });
                        languagesHTML += '</div>';
                    }

                    languagesSection.innerHTML = languagesHTML;
                    console.log('Languages section updated');
                } catch (error) {
                    console.error('Error updating languages preview:', error);
                }
            }

            // Update Certifications Section in Preview
            function updateCertificationsPreview(previewContainer) {
                try {
                    // Find or create certifications section in preview
                    let certificationsSection = null;
                    const allSections = previewContainer.querySelectorAll('div');

                    allSections.forEach(section => {
                        const h2 = section.querySelector('h2');
                        if (h2 && h2.textContent.includes('Certifications')) {
                            certificationsSection = section;
                        }
                    });

                    // If no certifications section exists, create one at the end
                    if (!certificationsSection) {
                        certificationsSection = document.createElement('div');
                        certificationsSection.className = 'mb-6';
                        previewContainer.appendChild(certificationsSection);
                    }

                    // Get certification entries
                    const certificationEntries = document.querySelectorAll('#certifications-container .flex.items-center.space-x-2');
                    let certificationsHTML = '<h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Certifications</h2>';

                    if (certificationEntries.length === 0) {
                        certificationsHTML += '<p class="text-sm text-gray-600">No certifications added yet.</p>';
                    } else {
                        certificationsHTML += '<ul class="text-sm text-gray-700 space-y-1">';
                        certificationEntries.forEach(entry => {
                            const certification = entry.querySelector('input')?.value || '';
                            if (certification && certification !== 'Certification Name') {
                                certificationsHTML += `<li>• ${certification}</li>`;
                            }
                        });
                        certificationsHTML += '</ul>';
                    }

                    certificationsSection.innerHTML = certificationsHTML;
                    console.log('Certifications section updated');
                } catch (error) {
                    console.error('Error updating certifications preview:', error);
                }
            }

            // PDF Generation Function
            async function generatePDF() {
                try {
                    console.log('Starting PDF generation...');
                    EasyNaukri.showNotification('Generating PDF... Please wait', 'info');

                    // Check if libraries are loaded
                    if (typeof html2canvas === 'undefined') {
                        throw new Error('html2canvas library not loaded');
                    }
                    if (typeof window.jspdf === 'undefined') {
                        throw new Error('jsPDF library not loaded');
                    }

                    // Get the resume preview element
                    const resumePreview = document.getElementById('resume-preview') ||
                                        document.querySelector('.resume-preview') ||
                                        document.querySelector('.border.border-gray-300');
                    console.log('Resume preview element:', resumePreview);

                    if (!resumePreview) {
                        EasyNaukri.showNotification('Resume preview not found!', 'error');
                        return;
                    }

                    // First update the preview to ensure latest data
                    updatePreview();

                    // Wait a moment for preview to update
                    await new Promise(resolve => setTimeout(resolve, 500));

                    console.log('Preparing resume for PDF capture...');

                    // Create a clean copy of the resume for PDF
                    const resumeClone = resumePreview.cloneNode(true);

                    // Style the clone for better PDF capture
                    resumeClone.style.transform = 'none';
                    resumeClone.style.width = '210mm'; // A4 width
                    resumeClone.style.maxWidth = '210mm';
                    resumeClone.style.padding = '20px';
                    resumeClone.style.margin = '0';
                    resumeClone.style.backgroundColor = '#ffffff';
                    resumeClone.style.fontFamily = 'Arial, sans-serif';
                    resumeClone.style.fontSize = '12px';
                    resumeClone.style.lineHeight = '1.4';
                    resumeClone.style.color = '#000000';

                    // Create temporary container
                    const tempContainer = document.createElement('div');
                    tempContainer.style.position = 'absolute';
                    tempContainer.style.top = '-9999px';
                    tempContainer.style.left = '-9999px';
                    tempContainer.style.width = '210mm';
                    tempContainer.style.backgroundColor = '#ffffff';
                    tempContainer.appendChild(resumeClone);
                    document.body.appendChild(tempContainer);

                    console.log('Capturing with html2canvas...');

                    // Use html2canvas to capture the clean resume
                    const canvas = await html2canvas(resumeClone, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: false,
                        backgroundColor: '#ffffff',
                        logging: false,
                        width: resumeClone.offsetWidth,
                        height: resumeClone.offsetHeight,
                        scrollX: 0,
                        scrollY: 0
                    });

                    // Remove temporary container
                    document.body.removeChild(tempContainer);

                    console.log('Canvas created:', canvas.width, 'x', canvas.height);

                    // Create PDF using jsPDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    // Calculate dimensions to fit A4
                    const imgWidth = 210; // A4 width in mm
                    const pageHeight = 295; // A4 height in mm
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    let heightLeft = imgHeight;
                    let position = 0;

                    console.log('Adding image to PDF...');
                    // Add image to PDF
                    const imgData = canvas.toDataURL('image/png');
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;

                    // Add new pages if content is longer than one page
                    while (heightLeft >= 0) {
                        position = heightLeft - imgHeight;
                        pdf.addPage();
                        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;
                    }

                    // Get name for filename
                    const firstName = document.querySelector('#personal input[placeholder="John"]')?.value || 'John';
                    const lastName = document.querySelector('#personal input[placeholder="Doe"]')?.value || 'Doe';
                    const filename = `${firstName}_${lastName}_Resume.pdf`;

                    console.log('Saving PDF as:', filename);
                    // Download the PDF
                    pdf.save(filename);

                    EasyNaukri.showNotification('PDF downloaded successfully!', 'success');

                } catch (error) {
                    console.error('Error generating PDF:', error);
                    EasyNaukri.showNotification(`Error generating PDF: ${error.message}`, 'error');

                    // Try text-based PDF as fallback
                    try {
                        generateTextPDF();
                    } catch (textError) {
                        // Final fallback: Open print dialog
                        setTimeout(() => {
                            EasyNaukri.showNotification('Opening print dialog as final fallback...', 'info');
                            generateSimplePDF();
                        }, 2000);
                    }
                }
            }

            // Enhanced text-based PDF generation
            function generateTextPDF() {
                try {
                    console.log('Generating enhanced text-based PDF...');

                    if (typeof window.jspdf === 'undefined') {
                        throw new Error('jsPDF not available');
                    }

                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    // Get personal data
                    const firstName = document.querySelector('#personal input[placeholder="John"]')?.value || 'John';
                    const lastName = document.querySelector('#personal input[placeholder="Doe"]')?.value || 'Doe';
                    const title = document.querySelector('#personal input[placeholder="Senior Software Engineer"]')?.value || 'Senior Software Engineer';
                    const email = document.querySelector('#personal input[type="email"]')?.value || '<EMAIL>';
                    const phone = document.querySelector('#personal input[type="tel"]')?.value || '+****************';
                    const location = document.querySelector('#personal input[placeholder="San Francisco, CA"]')?.value || 'San Francisco, CA';
                    const summary = document.querySelector('#personal textarea')?.value || 'Experienced professional with expertise in various technologies.';

                    let yPosition = 20;
                    const pageHeight = 280; // A4 height minus margins

                    // Header
                    pdf.setFontSize(18);
                    pdf.setFont(undefined, 'bold');
                    pdf.text(`${firstName} ${lastName}`, 105, yPosition, { align: 'center' });
                    yPosition += 8;

                    pdf.setFontSize(12);
                    pdf.setFont(undefined, 'normal');
                    pdf.text(title, 105, yPosition, { align: 'center' });
                    yPosition += 12;

                    // Contact info
                    pdf.setFontSize(9);
                    pdf.text(`${email} | ${phone} | ${location}`, 105, yPosition, { align: 'center' });
                    yPosition += 15;

                    // Professional Summary
                    pdf.setFontSize(11);
                    pdf.setFont(undefined, 'bold');
                    pdf.text('PROFESSIONAL SUMMARY', 20, yPosition);
                    yPosition += 6;

                    pdf.setFont(undefined, 'normal');
                    pdf.setFontSize(9);
                    const summaryLines = pdf.splitTextToSize(summary, 170);
                    pdf.text(summaryLines, 20, yPosition);
                    yPosition += summaryLines.length * 4 + 8;

                    // Experience Section
                    if (yPosition > pageHeight - 30) {
                        pdf.addPage();
                        yPosition = 20;
                    }

                    pdf.setFontSize(11);
                    pdf.setFont(undefined, 'bold');
                    pdf.text('EXPERIENCE', 20, yPosition);
                    yPosition += 6;

                    // Get experience entries
                    const experienceEntries = document.querySelectorAll('#experience .border.border-gray-200');
                    if (experienceEntries.length === 0) {
                        pdf.setFont(undefined, 'normal');
                        pdf.setFontSize(9);
                        pdf.text('No experience entries added.', 20, yPosition);
                        yPosition += 8;
                    } else {
                        experienceEntries.forEach((entry, index) => {
                            const jobTitle = entry.querySelector('input[placeholder*="Senior Software Engineer"]')?.value || 'Job Title';
                            const company = entry.querySelector('input[placeholder*="Google Inc"]')?.value || 'Company Name';
                            const startDate = entry.querySelector('input[type="month"]:first-of-type')?.value || '';
                            const endDate = entry.querySelector('input[type="month"]:last-of-type')?.value || '';
                            const isCurrentJob = entry.querySelector('input[type="checkbox"]')?.checked || false;
                            const description = entry.querySelector('textarea')?.value || '';

                            const endDateText = isCurrentJob ? 'Present' : (endDate || 'End Date');
                            const dateRange = startDate ? `${startDate} - ${endDateText}` : '';

                            // Check if we need a new page
                            if (yPosition > pageHeight - 25) {
                                pdf.addPage();
                                yPosition = 20;
                            }

                            pdf.setFont(undefined, 'bold');
                            pdf.setFontSize(10);
                            pdf.text(jobTitle, 20, yPosition);
                            if (dateRange) {
                                pdf.text(dateRange, 190, yPosition, { align: 'right' });
                            }
                            yPosition += 5;

                            pdf.setFont(undefined, 'normal');
                            pdf.setFontSize(9);
                            pdf.text(company, 20, yPosition);
                            yPosition += 4;

                            if (description) {
                                const descLines = pdf.splitTextToSize(description, 170);
                                pdf.text(descLines, 20, yPosition);
                                yPosition += descLines.length * 4;
                            }
                            yPosition += 6;
                        });
                    }

                    // Education Section
                    if (yPosition > pageHeight - 30) {
                        pdf.addPage();
                        yPosition = 20;
                    }

                    pdf.setFontSize(11);
                    pdf.setFont(undefined, 'bold');
                    pdf.text('EDUCATION', 20, yPosition);
                    yPosition += 6;

                    // Get education entries
                    const educationEntries = document.querySelectorAll('#education .border.border-gray-200');
                    if (educationEntries.length === 0) {
                        pdf.setFont(undefined, 'normal');
                        pdf.setFontSize(9);
                        pdf.text('No education entries added.', 20, yPosition);
                        yPosition += 8;
                    } else {
                        educationEntries.forEach((entry, index) => {
                            const degree = entry.querySelector('input[placeholder*="Bachelor"]')?.value || 'Degree';
                            const school = entry.querySelector('input[placeholder*="University"]')?.value || 'School/University';
                            const startDate = entry.querySelector('input[type="month"]:first-of-type')?.value || '';
                            const endDate = entry.querySelector('input[type="month"]:last-of-type')?.value || '';
                            const gpa = entry.querySelector('input[placeholder*="GPA"]')?.value || '';

                            const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : '';

                            pdf.setFont(undefined, 'bold');
                            pdf.setFontSize(10);
                            pdf.text(degree, 20, yPosition);
                            if (dateRange) {
                                pdf.text(dateRange, 190, yPosition, { align: 'right' });
                            }
                            yPosition += 5;

                            pdf.setFont(undefined, 'normal');
                            pdf.setFontSize(9);
                            pdf.text(school, 20, yPosition);
                            if (gpa) {
                                yPosition += 4;
                                pdf.text(`GPA: ${gpa}`, 20, yPosition);
                            }
                            yPosition += 8;
                        });
                    }

                    // Skills Section
                    if (yPosition > pageHeight - 20) {
                        pdf.addPage();
                        yPosition = 20;
                    }

                    pdf.setFontSize(11);
                    pdf.setFont(undefined, 'bold');
                    pdf.text('SKILLS', 20, yPosition);
                    yPosition += 6;

                    // Get skills
                    const skillTags = document.querySelectorAll('#technical-skills .bg-blue-100');
                    if (skillTags.length === 0) {
                        pdf.setFont(undefined, 'normal');
                        pdf.setFontSize(9);
                        pdf.text('No skills added.', 20, yPosition);
                    } else {
                        const skills = [];
                        skillTags.forEach(tag => {
                            const skillText = tag.textContent.replace('×', '').trim();
                            if (skillText) skills.push(skillText);
                        });

                        pdf.setFont(undefined, 'normal');
                        pdf.setFontSize(9);
                        const skillsText = skills.join(' • ');
                        const skillLines = pdf.splitTextToSize(skillsText, 170);
                        pdf.text(skillLines, 20, yPosition);
                    }

                    // Save PDF
                    const filename = `${firstName}_${lastName}_Resume.pdf`;
                    pdf.save(filename);

                    EasyNaukri.showNotification('Professional PDF generated successfully!', 'success');

                } catch (error) {
                    console.error('Error generating text PDF:', error);
                    throw error;
                }
            }

            // Alternative simple PDF generation using print
            function generateSimplePDF() {
                try {
                    console.log('Starting simple PDF generation...');

                    // Update preview first
                    updatePreview();

                    // Wait for preview to update
                    setTimeout(() => {
                        // Add print-specific class to body
                        document.body.classList.add('printing');

                        // Open print dialog
                        window.print();

                        // Remove print class after printing
                        setTimeout(() => {
                            document.body.classList.remove('printing');
                        }, 1000);

                        EasyNaukri.showNotification('Print dialog opened. Choose "Save as PDF" to download.', 'info');
                    }, 500);

                } catch (error) {
                    console.error('Error with simple PDF:', error);
                    EasyNaukri.showNotification('Error opening print dialog.', 'error');
                }
            }

            // Resume Builder Navigation System
            function initializeResumeBuilder() {
                const tabs = ['personal', 'experience', 'education', 'skills'];
                let currentTabIndex = 0;
                let completedTabs = []; // Track which tabs are completed

                const tabButtons = document.querySelectorAll('.tab-btn');
                const tabContents = document.querySelectorAll('.tab-content');
                const nextBtn = document.getElementById('next-btn');
                const prevBtn = document.getElementById('prev-btn');
                const finalActions = document.getElementById('final-actions');
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');

                // Update progress bar based on completed tabs
                function updateProgress() {
                    const progress = (completedTabs.length / tabs.length) * 100;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '% Complete';
                }

                // Show specific tab
                function showTab(index) {
                    // Hide all tabs
                    tabContents.forEach(content => content.classList.add('hidden'));
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-primary', 'text-primary');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });

                    // Show current tab
                    const targetTab = tabs[index];
                    const targetContent = document.getElementById(targetTab);
                    const targetButton = document.querySelector(`[data-tab="${targetTab}"]`);

                    if (targetContent) targetContent.classList.remove('hidden');
                    if (targetButton) {
                        targetButton.classList.add('border-primary', 'text-primary');
                        targetButton.classList.remove('border-transparent', 'text-gray-500');
                    }

                    // Update navigation buttons
                    prevBtn.classList.toggle('hidden', index === 0);
                    nextBtn.classList.toggle('hidden', index === tabs.length - 1);
                    finalActions.classList.toggle('hidden', index !== tabs.length - 1);
                    finalActions.classList.toggle('flex', index === tabs.length - 1);

                    // Don't update progress just by switching tabs
                }

                // Tab button clicks - only allow navigation to completed tabs or current tab
                tabButtons.forEach((button, index) => {
                    button.addEventListener('click', function() {
                        const targetTab = tabs[index];

                        // Allow navigation to:
                        // 1. Current tab
                        // 2. Completed tabs
                        // 3. First tab (always accessible)
                        if (index === currentTabIndex || completedTabs.includes(targetTab) || index === 0) {
                            currentTabIndex = index;
                            showTab(currentTabIndex);
                        } else {
                            EasyNaukri.showNotification('Please complete the previous sections first.', 'warning');
                        }
                    });
                });

                // Next button with validation
                nextBtn.addEventListener('click', function() {
                    if (validateCurrentTab()) {
                        // Mark current tab as completed
                        const currentTab = tabs[currentTabIndex];
                        if (!completedTabs.includes(currentTab)) {
                            completedTabs.push(currentTab);
                            updateProgress();

                            // Add visual indicator for completed tab
                            const completedButton = document.querySelector(`[data-tab="${currentTab}"]`);
                            if (completedButton) {
                                const icon = completedButton.querySelector('i');
                                if (icon && !completedButton.querySelector('.fa-check')) {
                                    const checkIcon = document.createElement('i');
                                    checkIcon.className = 'fas fa-check ml-1 text-green-500';
                                    completedButton.appendChild(checkIcon);
                                }
                            }

                            // EasyNaukri.showNotification(`${currentTab.charAt(0).toUpperCase() + currentTab.slice(1)} section completed!`, 'success');
                        }

                        if (currentTabIndex < tabs.length - 1) {
                            currentTabIndex++;
                            showTab(currentTabIndex);
                        }
                    }
                });

                // Validate current tab
                function validateCurrentTab() {
                    const currentTab = tabs[currentTabIndex];
                    const currentForm = document.querySelector(`#${currentTab} form`);

                    if (currentForm) {
                        const requiredFields = currentForm.querySelectorAll('input[required], textarea[required]');
                        let isValid = true;

                        requiredFields.forEach(field => {
                            if (!field.value.trim()) {
                                field.classList.add('border-red-500');
                                isValid = false;
                            } else {
                                field.classList.remove('border-red-500');
                            }
                        });

                        if (!isValid) {
                            EasyNaukri.showNotification('Please fill all required fields before proceeding.', 'error');
                            return false;
                        }
                    }

                    return true;
                }

                // Previous button
                prevBtn.addEventListener('click', function() {
                    if (currentTabIndex > 0) {
                        currentTabIndex--;
                        showTab(currentTabIndex);
                    }
                });

                // Initialize first tab
                showTab(0);
            }
        });
    </script>
</body>
</html>
