<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-primary font-medium">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <!-- For Employers dropdown removed for security -->
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
            <p class="text-gray-600">Loading job details...</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="jobs.html" class="inline-flex items-center text-gray-600 hover:text-primary transition">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Job Listings
            </a>
        </div>

        <!-- Job Details Container -->
        <div id="jobDetailsContainer" class="hidden">
            <!-- Job Header -->
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                    <div class="flex-1">
                        <h1 id="jobTitle" class="text-3xl font-bold text-gray-900 mb-4">Job Title</h1>
                        <div class="flex flex-wrap items-center gap-4 mb-6">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-building mr-2"></i>
                                <span id="jobCompany">Company Name</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <span id="jobLocation">Location</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-briefcase mr-2"></i>
                                <span id="jobType">Job Type</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-clock mr-2"></i>
                                <span id="jobPosted">Posted Date</span>
                            </div>
                        </div>
                        
                        <!-- Salary -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-dollar-sign text-green-600 mr-2"></i>
                                <span id="jobSalary" class="text-2xl font-bold text-green-600">Salary Range</span>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="flex flex-wrap gap-6 mb-6">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-eye mr-2"></i>
                                <span id="jobViews">0</span> views
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-users mr-2"></i>
                                <span id="jobApplications">0</span> applications
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-layer-group mr-2"></i>
                                <span id="jobExperience">Experience Level</span>
                            </div>
                        </div>
                    </div>

                    <!-- Apply Button -->
                    <div class="lg:ml-8 mt-6 lg:mt-0">
                        <button id="applyBtn" class="w-full lg:w-auto bg-primary text-white px-8 py-3 rounded-lg hover:bg-secondary transition font-medium text-lg">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Apply Now
                        </button>
                        <button id="saveJobBtn" class="w-full lg:w-auto mt-3 lg:mt-2 border border-gray-300 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-50 transition font-medium">
                            <i class="far fa-heart mr-2"></i>
                            Save Job
                        </button>
                    </div>
                </div>
            </div>

            <!-- Job Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Job Description -->
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Job Description</h2>
                        <div id="jobDescription" class="prose prose-lg max-w-none text-gray-700">
                            <!-- Job description will be loaded here -->
                        </div>
                    </div>

                    <!-- Required Skills -->
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Required Skills</h2>
                        <div id="jobSkills" class="flex flex-wrap gap-3">
                            <!-- Skills will be loaded here -->
                        </div>
                    </div>

                    <!-- How to Apply -->
                    <div class="bg-blue-50 rounded-lg p-8 border border-blue-200">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">How to Apply</h2>
                        <p class="text-gray-700 mb-4">Ready to take the next step in your career? Click the "Apply Now" button above to be redirected to the company's application page.</p>

                        <!-- Application URL (Primary) -->
                        <div id="applicationUrlSection" class="bg-white rounded-lg p-4 border border-blue-300 mb-4 hidden">
                            <div class="flex items-center">
                                <i class="fas fa-external-link-alt text-blue-600 mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Apply directly on company website:</p>
                                    <a id="applicationUrl" href="#" target="_blank" class="text-blue-600 hover:text-blue-800 font-medium">
                                        <!-- Application URL will be loaded here -->
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Application Email (Fallback) -->
                        <div id="applicationEmailSection" class="bg-white rounded-lg p-4 border border-blue-300 hidden">
                            <div class="flex items-center">
                                <i class="fas fa-envelope text-blue-600 mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Or send your application via email:</p>
                                    <a id="applicationEmail" href="#" class="text-blue-600 hover:text-blue-800 font-medium">
                                        <!-- Application email will be loaded here -->
                                    </a>
                                </div>
                            </div>
                        </div>

                        <p class="text-sm text-gray-600 mt-4">
                            <i class="fas fa-info-circle mr-2"></i>
                            Make sure to have your resume and cover letter ready before applying.
                        </p>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-8">
                    <!-- Company Info -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Company Information</h3>
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-1">Company</h4>
                                <p id="sidebarCompany" class="text-gray-600">Company Name</p>
                            </div>
                            <div id="industrySection" class="hidden">
                                <h4 class="font-medium text-gray-900 mb-1">Industry</h4>
                                <p id="sidebarIndustry" class="text-gray-600">Industry</p>
                            </div>
                            <div id="companySizeSection" class="hidden">
                                <h4 class="font-medium text-gray-900 mb-1">Company Size</h4>
                                <p id="sidebarCompanySize" class="text-gray-600">Company Size</p>
                            </div>
                            <div id="websiteSection" class="hidden">
                                <h4 class="font-medium text-gray-900 mb-1">Website</h4>
                                <a id="sidebarWebsite" href="#" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    Visit Company Website
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Job Details -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Job Details</h3>
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-1">Job Type</h4>
                                <p id="sidebarJobType" class="text-gray-600">Full-time</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-1">Experience Level</h4>
                                <p id="sidebarExperience" class="text-gray-600">Mid Level</p>
                            </div>
                            <div id="categorySection" class="hidden">
                                <h4 class="font-medium text-gray-900 mb-1">Category</h4>
                                <p id="sidebarCategory" class="text-gray-600">Category</p>
                            </div>
                            <div id="workLocationSection" class="hidden">
                                <h4 class="font-medium text-gray-900 mb-1">Work Location</h4>
                                <p id="sidebarWorkLocation" class="text-gray-600">On-site</p>
                            </div>
                        </div>
                    </div>

                    <!-- Share Job -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Share This Job</h3>
                        <div class="flex space-x-3">
                            <button onclick="shareJob('facebook')" class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition text-sm">
                                <i class="fab fa-facebook-f mr-1"></i>
                                Facebook
                            </button>
                            <button onclick="shareJob('twitter')" class="flex-1 bg-blue-400 text-white py-2 px-3 rounded-lg hover:bg-blue-500 transition text-sm">
                                <i class="fab fa-twitter mr-1"></i>
                                Twitter
                            </button>
                            <button onclick="shareJob('linkedin')" class="flex-1 bg-blue-700 text-white py-2 px-3 rounded-lg hover:bg-blue-800 transition text-sm">
                                <i class="fab fa-linkedin-in mr-1"></i>
                                LinkedIn
                            </button>
                        </div>
                        <button onclick="copyJobLink()" class="w-full mt-3 border border-gray-300 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-50 transition text-sm">
                            <i class="fas fa-link mr-1"></i>
                            Copy Link
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden text-center py-12">
            <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Job Not Found</h2>
            <p class="text-gray-600 mb-6">The job you're looking for doesn't exist or has been removed.</p>
            <a href="jobs.html" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                <i class="fas fa-search mr-2"></i>
                Browse All Jobs
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <!-- <li><a href="login.html" class="hover:text-white transition">Sign In</a></li> -->
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script type="module">
        import { firebaseJobs, FirebaseUtils } from './js/firebase-config.js';

        let currentJob = null;

        // Get job ID from URL parameters
        function getJobIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id');
        }

        // Load job details from Firebase
        async function loadJobDetails() {
            const jobId = getJobIdFromUrl();
            
            if (!jobId) {
                showErrorState();
                return;
            }

            try {
                console.log('Loading job details for ID:', jobId);
                
                // Get all jobs and find the specific one
                const jobs = await firebaseJobs.getJobs({ status: 'active' });
                currentJob = jobs.find(job => job.id === jobId);

                if (!currentJob) {
                    showErrorState();
                    return;
                }

                // Update view count
                await updateJobViews(jobId);

                // Populate job details
                populateJobDetails(currentJob);
                
                // Show job details
                document.getElementById('loadingScreen').classList.add('hidden');
                document.getElementById('jobDetailsContainer').classList.remove('hidden');

            } catch (error) {
                console.error('Error loading job details:', error);
                showErrorState();
            }
        }

        // Update job view count
        async function updateJobViews(jobId) {
            try {
                const currentViews = currentJob.views || 0;
                await firebaseJobs.updateJob(jobId, { views: currentViews + 1 });
            } catch (error) {
                console.error('Error updating view count:', error);
            }
        }

        // Populate job details in the UI
        function populateJobDetails(job) {
            // Header information
            document.getElementById('jobTitle').textContent = job.title;
            document.getElementById('jobCompany').textContent = job.company;
            document.getElementById('jobLocation').textContent = job.location;
            document.getElementById('jobType').textContent = job.type || 'Full-time';
            document.getElementById('jobPosted').textContent = FirebaseUtils.formatDate(job.createdAt);
            
            // Salary
            const salaryElement = document.getElementById('jobSalary');

            // Debug log to see salary values
            console.log('Job details salary data:', {
                salary: job.salary || job.salaryMax || job.salaryMin,
                salaryPeriod: job.salaryPeriod
            });

            // Handle both new salary field and old salaryMin/salaryMax for backward compatibility
            let salaryAmount = 0;

            // Priority: salary > salaryMax > salaryMin
            if (job.salary && job.salary > 0) {
                salaryAmount = job.salary;
            } else if (job.salaryMax && job.salaryMax > 0) {
                salaryAmount = job.salaryMax;
            } else if (job.salaryMin && job.salaryMin > 0) {
                salaryAmount = job.salaryMin;
            }

            // Format salary with period
            if (!salaryAmount || salaryAmount === 0) {
                salaryElement.textContent = 'Salary not specified';
            } else {
                let periodText = '';

                // Determine period text
                switch (job.salaryPeriod) {
                    case 'monthly':
                        periodText = ' per month';
                        break;
                    case 'hourly':
                        periodText = ' per hour';
                        break;
                    case 'yearly':
                    default:
                        periodText = ' per year';
                        break;
                }

                // Format amount
                const salaryText = `$${salaryAmount.toLocaleString()}${periodText}`;
                salaryElement.textContent = salaryText;
            }

            // Stats
            document.getElementById('jobViews').textContent = (job.views || 0).toLocaleString();
            document.getElementById('jobApplications').textContent = (job.applications || 0).toLocaleString();
            document.getElementById('jobExperience').textContent = job.experience || 'Not specified';

            // Description
            document.getElementById('jobDescription').innerHTML = job.description.replace(/\n/g, '<br>');

            // Skills
            const skillsContainer = document.getElementById('jobSkills');
            if (job.skills && job.skills.length > 0) {
                skillsContainer.innerHTML = job.skills.map(skill => 
                    `<span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">${skill}</span>`
                ).join('');
            } else {
                skillsContainer.innerHTML = '<p class="text-gray-500">No specific skills listed</p>';
            }

            // Application URL (Primary method)
            if (job.applicationUrl) {
                const urlSection = document.getElementById('applicationUrlSection');
                const urlElement = document.getElementById('applicationUrl');
                urlSection.classList.remove('hidden');
                urlElement.textContent = job.applicationUrl;
                urlElement.href = job.applicationUrl;
            }

            // Application email (Fallback method)
            if (job.applicationEmail) {
                const emailSection = document.getElementById('applicationEmailSection');
                const emailElement = document.getElementById('applicationEmail');
                emailSection.classList.remove('hidden');
                emailElement.textContent = job.applicationEmail;
                emailElement.href = `mailto:${job.applicationEmail}?subject=Application for ${job.title}`;
            }

            // Sidebar information
            document.getElementById('sidebarCompany').textContent = job.company;
            document.getElementById('sidebarJobType').textContent = job.type || 'Full-time';
            document.getElementById('sidebarExperience').textContent = job.experience || 'Not specified';

            // Optional fields
            if (job.industry) {
                document.getElementById('industrySection').classList.remove('hidden');
                document.getElementById('sidebarIndustry').textContent = job.industry;
            }

            if (job.companySize) {
                document.getElementById('companySizeSection').classList.remove('hidden');
                document.getElementById('sidebarCompanySize').textContent = job.companySize;
            }

            if (job.companyWebsite) {
                document.getElementById('websiteSection').classList.remove('hidden');
                document.getElementById('sidebarWebsite').href = job.companyWebsite;
            }

            if (job.category) {
                document.getElementById('categorySection').classList.remove('hidden');
                document.getElementById('sidebarCategory').textContent = job.category;
            }

            if (job.workLocation) {
                document.getElementById('workLocationSection').classList.remove('hidden');
                document.getElementById('sidebarWorkLocation').textContent = job.workLocation;
            }
        }

        // Show error state
        function showErrorState() {
            document.getElementById('loadingScreen').classList.add('hidden');
            document.getElementById('jobDetailsContainer').classList.add('hidden');
            document.getElementById('errorState').classList.remove('hidden');
        }

        // Apply button functionality
        document.getElementById('applyBtn').addEventListener('click', async function() {
            if (!currentJob) return;

            try {
                // Update application count
                const currentApplications = currentJob.applications || 0;
                await firebaseJobs.updateJob(currentJob.id, { applications: currentApplications + 1 });

                // Check if application URL is available
                if (currentJob.applicationUrl) {
                    // Redirect to company's application page
                    FirebaseUtils.showNotification('Redirecting to company application page...', 'success');
                    window.open(currentJob.applicationUrl, '_blank');
                } else if (currentJob.applicationEmail) {
                    // Fallback to email if no URL provided
                    const subject = encodeURIComponent(`Application for ${currentJob.title}`);
                    const body = encodeURIComponent(`Dear Hiring Manager,\n\nI am interested in applying for the ${currentJob.title} position at ${currentJob.company}.\n\nPlease find my resume attached.\n\nBest regards,\n[Your Name]`);
                    window.location.href = `mailto:${currentJob.applicationEmail}?subject=${subject}&body=${body}`;
                    FirebaseUtils.showNotification('Opening email client...', 'success');
                } else {
                    FirebaseUtils.showNotification('No application method available for this job.', 'error');
                    return;
                }

                // Update the applications count in UI
                document.getElementById('jobApplications').textContent = (currentApplications + 1).toLocaleString();

            } catch (error) {
                console.error('Error updating application count:', error);
                // Still try to redirect/open application method even if count update fails
                if (currentJob.applicationUrl) {
                    window.open(currentJob.applicationUrl, '_blank');
                } else if (currentJob.applicationEmail) {
                    const subject = encodeURIComponent(`Application for ${currentJob.title}`);
                    window.location.href = `mailto:${currentJob.applicationEmail}?subject=${subject}`;
                }
            }
        });

        // Save job functionality
        document.getElementById('saveJobBtn').addEventListener('click', function() {
            if (!currentJob) return;

            // Get saved jobs from localStorage
            let savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');
            
            // Check if job is already saved
            const isAlreadySaved = savedJobs.some(job => job.id === currentJob.id);
            
            if (isAlreadySaved) {
                // Remove from saved jobs
                savedJobs = savedJobs.filter(job => job.id !== currentJob.id);
                localStorage.setItem('savedJobs', JSON.stringify(savedJobs));
                
                this.innerHTML = '<i class="far fa-heart mr-2"></i>Save Job';
                this.classList.remove('bg-red-50', 'text-red-600', 'border-red-300');
                this.classList.add('border-gray-300', 'text-gray-700');
                
                FirebaseUtils.showNotification('Job removed from saved jobs', 'info');
            } else {
                // Add to saved jobs
                savedJobs.push(currentJob);
                localStorage.setItem('savedJobs', JSON.stringify(savedJobs));
                
                this.innerHTML = '<i class="fas fa-heart mr-2"></i>Saved';
                this.classList.remove('border-gray-300', 'text-gray-700');
                this.classList.add('bg-red-50', 'text-red-600', 'border-red-300');
                
                FirebaseUtils.showNotification('Job saved successfully!', 'success');
            }
        });

        // Share job functions
        window.shareJob = function(platform) {
            if (!currentJob) return;

            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(`${currentJob.title} at ${currentJob.company}`);
            const description = encodeURIComponent(`Check out this job opportunity: ${currentJob.title} at ${currentJob.company}`);

            let shareUrl = '';
            
            switch (platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                    break;
                case 'linkedin':
                    shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
                    break;
            }

            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        };

        // Copy job link
        window.copyJobLink = function() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                FirebaseUtils.showNotification('Job link copied to clipboard!', 'success');
            }).catch(() => {
                FirebaseUtils.showNotification('Failed to copy link', 'error');
            });
        };

        // Check if job is already saved on page load
        function checkSavedStatus() {
            if (!currentJob) return;

            const savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');
            const isAlreadySaved = savedJobs.some(job => job.id === currentJob.id);
            
            if (isAlreadySaved) {
                const saveBtn = document.getElementById('saveJobBtn');
                saveBtn.innerHTML = '<i class="fas fa-heart mr-2"></i>Saved';
                saveBtn.classList.remove('border-gray-300', 'text-gray-700');
                saveBtn.classList.add('bg-red-50', 'text-red-600', 'border-red-300');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            await loadJobDetails();
            checkSavedStatus();
        });
    </script>
</body>
</html>