<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USA EasyNaukri4U - Find Your Dream Job in America</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">


    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white drop-shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-primary font-medium">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <!-- For Employers dropdown removed for security -->
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Find Your Dream Job in <span class="text-accent">America</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100">
                Connect with top employers across the United States. Your career journey starts here.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-4xl mx-auto bg-white rounded-lg p-4 shadow-xl">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="Job title, keywords, or company"
                                   class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="City, state, or zip code"
                                   class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </div>
                    <button class="search-btn bg-accent text-white px-8 py-3 rounded-lg hover:bg-yellow-500 transition font-medium">
                        <i class="fas fa-search mr-2"></i>Search Jobs
                    </button>
                </div>

                <!-- Quick Filters -->
                <div class="flex flex-wrap gap-2 mt-4 justify-center">
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Remote</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Full-time</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Part-time</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Contract</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Internship</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">50K+</div>
                    <div class="text-gray-600">Active Jobs</div>
                </div>
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">25K+</div>
                    <div class="text-gray-600">Companies</div>
                </div>
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">100K+</div>
                    <div class="text-gray-600">Job Seekers</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Jobs Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Jobs</h2>
                <p class="text-xl text-gray-600">Discover opportunities from top companies</p>
            </div>

            <!-- Dynamic Jobs Container -->
            <div id="jobs-container" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Loading placeholder -->
                <div class="col-span-full text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <p class="mt-2 text-gray-600">Loading latest jobs...</p>
                </div>
            </div>
            <div class="text-center">
                <a href="jobs.html" class="inline-flex items-center bg-white text-primary border-2 border-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition">
                    View All Jobs <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Browse by Category</h2>
                <p class="text-xl text-gray-600">Find jobs in your field of expertise</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <div class="text-center group cursor-pointer">
                    <div class="bg-blue-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-code text-2xl text-blue-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Technology</h3>
                    <p class="text-sm text-gray-600">12,450 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-green-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-chart-line text-2xl text-green-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Finance</h3>
                    <p class="text-sm text-gray-600">8,230 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-red-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-heartbeat text-2xl text-red-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Healthcare</h3>
                    <p class="text-sm text-gray-600">6,890 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-purple-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-bullhorn text-2xl text-purple-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Marketing</h3>
                    <p class="text-sm text-gray-600">5,670 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-yellow-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-graduation-cap text-2xl text-yellow-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Education</h3>
                    <p class="text-sm text-gray-600">4,320 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-indigo-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-palette text-2xl text-indigo-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Design</h3>
                    <p class="text-sm text-gray-600">3,450 jobs</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Everything You Need to Land Your Dream Job</h2>
                <p class="text-xl text-gray-600">Comprehensive tools and resources to accelerate your career</p>
            </div>

            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Resume Builder Feature -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-8 text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Professional Resume Builder</h3>
                    <p class="text-gray-600 mb-6">Create stunning, ATS-friendly resumes with our easy-to-use builder. Choose from professional templates and get hired faster.</p>
                    <ul class="text-left text-gray-600 mb-6 space-y-2">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>ATS-Optimized Templates</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Real-time Preview</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>PDF Export</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Industry-Specific Formats</li>
                    </ul>
                    <a href="resume-builder.html" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition inline-block">
                        Build Resume <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Study Materials Feature -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-8 text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-graduation-cap text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Study Materials & Prep</h3>
                    <p class="text-gray-600 mb-6">Master interview skills with our comprehensive study materials, practice tests, and mock interviews.</p>
                    <ul class="text-left text-gray-600 mb-6 space-y-2">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>500+ Practice Questions</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mock Interviews</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Technical & Soft Skills</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Video Tutorials</li>
                    </ul>
                    <a href="study-material.html" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition inline-block">
                        Start Learning <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
                <p class="text-xl text-gray-600">Get hired in 4 simple steps</p>
            </div>

            <div class="grid md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">1</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Create Your Profile</h3>
                    <p class="text-gray-600">Sign up and build your professional profile with our easy-to-use tools.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">2</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Search & Apply</h3>
                    <p class="text-gray-600">Browse thousands of jobs and apply with one click using your profile.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">3</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Prepare & Practice</h3>
                    <p class="text-gray-600">Use our study materials and mock interviews to ace your interviews.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">4</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Get Hired</h3>
                    <p class="text-gray-600">Land your dream job and start your new career journey.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose USA EasyNaukri4U?</h2>
                <p class="text-xl text-gray-600">The most comprehensive job portal in America</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-shield-alt text-3xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Trusted Platform</h3>
                    <p class="text-gray-600">Verified companies and secure application process. Your data is safe with us.</p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-rocket text-3xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Fast Results</h3>
                    <p class="text-gray-600">Get matched with relevant jobs instantly. Our AI-powered system finds the perfect fit.</p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-users text-3xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Expert Support</h3>
                    <p class="text-gray-600">24/7 customer support and career guidance from industry experts.</p>
                </div>

                <div class="text-center">
                    <div class="bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-chart-line text-3xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Career Growth</h3>
                    <p class="text-gray-600">Continuous learning resources and career development tools to advance your career.</p>
                </div>
            </div>

            <!-- Additional Features Grid -->
            <div class="mt-16 grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-mobile-alt text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Mobile Optimized</h4>
                    </div>
                    <p class="text-gray-600">Search and apply for jobs on the go with our mobile-friendly platform.</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-bell text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Smart Alerts</h4>
                    </div>
                    <p class="text-gray-600">Get notified instantly when new jobs matching your criteria are posted.</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-analytics text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Application Insights</h4>
                    </div>
                    <p class="text-gray-600">Track your application status and get insights to improve your success rate.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- For Employers Section -->
    <section class="py-16 bg-gradient-to-r from-secondary to-primary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">Hire Top Talent</h2>
                    <p class="text-xl text-blue-100 mb-8">Connect with qualified candidates and build your dream team. Our platform makes hiring simple and effective.</p>
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Access to 100K+ qualified candidates</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Advanced filtering and search tools</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Dedicated account management</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Competitive pricing plans</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="jobs.html" class="bg-white/10 backdrop-blur-sm text-white px-8 py-3 rounded-lg hover:bg-white/20 transition font-medium text-center border border-white/20">Browse Jobs</a>
                            Get Started
                        </a>
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">25K+</div>
                                <div class="text-blue-100">Companies Trust Us</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">48hrs</div>
                                <div class="text-blue-100">Average Time to Hire</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">95%</div>
                                <div class="text-blue-100">Employer Satisfaction</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">24/7</div>
                                <div class="text-blue-100">Support Available</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Success Stories</h2>
                <p class="text-xl text-gray-600">Real people, real success stories</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="Sarah Johnson" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
                            <p class="text-gray-600 text-sm">Software Engineer at Google</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"EasyNaukri4U helped me land my dream job at Google. The resume builder and interview prep materials were incredibly helpful!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="Michael Chen" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Michael Chen</h4>
                            <p class="text-gray-600 text-sm">Data Scientist at Microsoft</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"The study materials and mock interviews gave me the confidence I needed. Got hired within 2 weeks of using the platform!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face" alt="Emily Rodriguez" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Emily Rodriguez</h4>
                            <p class="text-gray-600 text-sm">Marketing Manager at Amazon</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"Amazing platform! The job recommendations were spot-on and the application process was seamless. Highly recommended!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-12 text-white">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Stay Updated</h2>
                <p class="text-xl text-blue-100 mb-8">Get the latest job opportunities and career tips delivered to your inbox</p>
                <div class="max-w-md mx-auto">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input type="email" placeholder="Enter your email address"
                               class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:ring-2 focus:ring-accent focus:outline-none">
                        <button class="bg-accent text-gray-900 px-8 py-3 rounded-lg hover:bg-yellow-400 transition font-medium">
                            Subscribe
                        </button>
                    </div>
                    <p class="text-sm text-blue-100 mt-4">No spam, unsubscribe at any time</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <!-- <li><a href="login.html" class="hover:text-white transition">Sign In</a></li> -->
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Initialize search functionality on homepage
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof EasyNaukri !== 'undefined' && EasyNaukri.initializeSearch) {
                EasyNaukri.initializeSearch();
            }
        });
    </script>

    <!-- Jobs Loading Script -->
    <script type="module">
        // Import Firebase configuration from the same file as jobs.html
        import { firebaseJobs } from './js/firebase-config.js';

        // Wait for DOM to be loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing homepage jobs...');
            setTimeout(loadFeaturedJobs, 500); // Small delay to ensure everything is ready
        });

        async function loadFeaturedJobs() {
            try {
                console.log('Loading featured jobs from Firebase...');

                // Use the same method as jobs.html
                const jobs = await firebaseJobs.getJobs({
                    status: null, // Get all jobs regardless of status
                    limitCount: 3 // Limit to 3 jobs for homepage
                });

                console.log(`Loaded ${jobs.length} jobs from Firebase`);
                console.log('Jobs data:', jobs);

                displayJobs(jobs);

            } catch (error) {
                console.error('Error loading jobs:', error);
                console.error('Error details:', error.message);
                showNoJobsMessage();
            }
        }

        function displayJobs(jobs) {
            const jobsContainer = document.getElementById('jobs-container');

            if (jobs.length === 0) {
                showNoJobsMessage();
                return;
            }

            // Clear loading placeholder
            jobsContainer.innerHTML = '';

            jobs.forEach((job, index) => {
                const jobCard = createJobCard(job, index);
                jobsContainer.appendChild(jobCard);
            });
        }

        function createJobCard(job, index) {
            const div = document.createElement('div');
            div.className = 'bg-white rounded-lg shadow-md hover:shadow-lg transition p-6 cursor-pointer h-full flex flex-col';

            // Add click handler to redirect to jobs page
            div.addEventListener('click', function() {
                console.log('Job card clicked, redirecting to jobs page');
                window.location.href = 'jobs.html';
            });

            // Get company icon based on company name
            const companyIcon = getCompanyIcon(job.company);
            const companyColor = getCompanyColor(index);

            // Format salary using jobs page logic
            const salaryInfo = formatSalaryDisplay(job);

            // Format date
            const postedDate = formatDate(job.createdAt);

            div.innerHTML = `
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 ${companyColor.bg} rounded-lg flex items-center justify-center">
                            <i class="${companyIcon} ${companyColor.text} text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">${job.title || 'Job Title'}</h3>
                            <p class="text-gray-600">${job.company || 'Company Name'}</p>
                        </div>
                    </div>
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">${job.type || 'Full-time'}</span>
                </div>
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span>${job.location || 'Location not specified'}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        <span>${salaryInfo.shortAmount}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-clock mr-2"></i>
                        <span>Posted ${postedDate}</span>
                    </div>
                </div>
                <div class="flex-grow">
                    ${job.description ? `<p class="text-gray-700 mb-4 text-sm">${String(job.description).substring(0, 120)}${String(job.description).length > 120 ? '...' : ''}</p>` : ''}
                    <div class="flex flex-wrap gap-2 mb-4">
                        ${getSkillsHTML(job.skills)}
                    </div>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <span class="text-primary font-medium text-sm">Click to view all jobs →</span>
                    </div>
                </div>
            `;

            return div;
        }

        function getSkillsHTML(skills) {
            if (!skills) return '';

            let skillsArray = [];

            // Handle different skill formats
            if (Array.isArray(skills)) {
                // Skills is already an array
                skillsArray = skills;
            } else if (typeof skills === 'string') {
                // Skills is a string, split by comma
                skillsArray = skills.split(',');
            } else {
                // Unknown format, return empty
                return '';
            }

            // Take first 3 skills and create HTML
            return skillsArray
                .slice(0, 3)
                .map(skill => {
                    const trimmedSkill = typeof skill === 'string' ? skill.trim() : String(skill).trim();
                    return trimmedSkill ? `<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${trimmedSkill}</span>` : '';
                })
                .filter(html => html !== '')
                .join('');
        }

        function getCompanyIcon(companyName) {
            if (!companyName) return 'fas fa-building';
            const company = companyName.toLowerCase();
            if (company.includes('google')) return 'fab fa-google';
            if (company.includes('microsoft')) return 'fab fa-microsoft';
            if (company.includes('amazon')) return 'fab fa-amazon';
            if (company.includes('apple')) return 'fab fa-apple';
            if (company.includes('facebook') || company.includes('meta')) return 'fab fa-facebook';
            if (company.includes('netflix')) return 'fas fa-film';
            if (company.includes('uber')) return 'fab fa-uber';
            if (company.includes('airbnb')) return 'fab fa-airbnb';
            return 'fas fa-building';
        }

        function getCompanyColor(index) {
            const colors = [
                { bg: 'bg-blue-100', text: 'text-blue-600' },
                { bg: 'bg-green-100', text: 'text-green-600' },
                { bg: 'bg-purple-100', text: 'text-purple-600' },
                { bg: 'bg-red-100', text: 'text-red-600' },
                { bg: 'bg-yellow-100', text: 'text-yellow-600' },
                { bg: 'bg-indigo-100', text: 'text-indigo-600' }
            ];
            return colors[index % colors.length];
        }

        function formatSalaryDisplay(job) {
            // Match jobs page formatting by echoing provided salary text with optional period
            let salaryText = '';
            if (job.salary === null || job.salary === undefined) {
                salaryText = 'Not specified';
            } else {
                salaryText = String(job.salary);
            }

            if (!salaryText || salaryText.trim() === '' || salaryText === 'Not specified') {
                return {
                    amount: 'Not specified',
                    period: '',
                    shortAmount: 'N/A'
                };
            }

            // Determine period text similar to jobs page
            let periodText = '';
            if (job.salaryPeriod) {
                switch (job.salaryPeriod) {
                    case 'monthly':
                        periodText = 'per month';
                        break;
                    case 'hourly':
                        periodText = 'per hour';
                        break;
                    case 'yearly':
                        periodText = 'per year';
                        break;
                    default:
                        periodText = 'per year';
                        break;
                }
            }

            // Add $ sign if not present and looks numeric/range
            let displaySalary = salaryText;
            if (!displaySalary.includes('$') && (displaySalary.match(/^\d/) || displaySalary.includes('-'))) {
                displaySalary = '$' + displaySalary;
            }

            return {
                amount: displaySalary,
                period: periodText,
                shortAmount: displaySalary
            };
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Recently';

            let date;
            if (timestamp.toDate) {
                // Firestore timestamp
                date = timestamp.toDate();
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                // Try to parse as date
                date = new Date(timestamp);
            }

            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) return '1 day ago';
            if (diffDays < 7) return `${diffDays} days ago`;
            if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
            return `${Math.ceil(diffDays / 30)} months ago`;
        }

        function showNoJobsMessage() {
            const jobsContainer = document.getElementById('jobs-container');
            jobsContainer.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <i class="fas fa-briefcase text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Jobs Available</h3>
                    <p class="text-gray-600 mb-6">We're currently updating our job listings. Please check back soon!</p>
                    <div class="space-y-3">
                        <a href="jobs.html" class="inline-flex items-center bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                            Browse All Jobs <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                        <br>
                        <button onclick="loadFeaturedJobs()" class="inline-flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition text-sm">
                            <i class="fas fa-sync-alt mr-2"></i>Retry Loading
                        </button>
                    </div>
                </div>
            `;
        }

        // Debug function to check Firebase connection
        window.debugHomepageJobs = async function() {
            console.log('=== HOMEPAGE JOBS DEBUG ===');
            try {
                const { firebaseJobs } = await import('./js/firebase-config.js');
                const jobs = await firebaseJobs.getJobs({ status: null });
                console.log('Total jobs in database:', jobs.length);
                console.log('Jobs:', jobs);
            } catch (error) {
                console.error('Debug error:', error);
            }
        };

        // Make loadFeaturedJobs globally accessible for retry button
        window.loadFeaturedJobs = loadFeaturedJobs;
    </script>
    </script>
</body>
</html>
