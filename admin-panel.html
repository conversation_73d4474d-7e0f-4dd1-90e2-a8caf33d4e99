<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
            <p class="text-gray-600">Loading Admin Panel...</p>
        </div>
    </div>

    <!-- Admin Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium ml-2">ADMIN</span>
                    </div>
                </div>

                <!-- Admin Info & Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Admin User Info -->
                    <div id="adminInfo" class="hidden md:flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-user-shield text-red-600"></i>
                        <span>Welcome, <span id="adminEmail">Admin</span></span>
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex items-center space-x-2">
                        <a href="index.html" target="_blank" class="text-gray-600 hover:text-primary transition" title="View Website">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <button id="logoutBtn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm">
                            <i class="fas fa-sign-out-alt mr-1"></i>Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
            <p class="text-gray-600">Manage your job portal system</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-briefcase text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Jobs</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalJobs">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Jobs</p>
                        <p class="text-2xl font-bold text-gray-900" id="activeJobs">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-eye text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Views</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalViews">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-file-alt text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Applications</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalApplications">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Job Management -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-bold text-gray-900">Job Management</h2>
                            <button id="addJobBtn" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                                <i class="fas fa-plus mr-2"></i>Post New Job
                            </button>
                        </div>
                    </div>
                    
                    <!-- Jobs List -->
                    <div class="p-6">
                        <div id="jobsList" class="space-y-4">
                            <!-- Jobs will be loaded here -->
                        </div>
                        
                        <!-- Empty State -->
                        <div id="emptyJobsState" class="hidden text-center py-12">
                            <i class="fas fa-briefcase text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No jobs posted yet</h3>
                            <p class="text-gray-600 mb-4">Start by posting your first job opening</p>
                            <button onclick="document.getElementById('addJobBtn').click()" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">
                                <i class="fas fa-plus mr-2"></i>Post Your First Job
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-6">
                <!-- System Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">System Status</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Firebase Connection</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-circle text-xs mr-1"></i>Connected
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Database Status</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-circle text-xs mr-1"></i>Online
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Last Backup</span>
                            <span class="text-sm text-gray-600" id="lastBackup">Just now</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Links</h3>
                    <div class="space-y-2">
                        <a href="jobs.html" target="_blank" class="flex items-center p-2 text-gray-700 hover:bg-gray-50 rounded-lg transition">
                            <i class="fas fa-search mr-3 text-gray-400"></i>
                            <span>View Job Listings</span>
                            <i class="fas fa-external-link-alt ml-auto text-xs text-gray-400"></i>
                        </a>
                        <a href="index.html" target="_blank" class="flex items-center p-2 text-gray-700 hover:bg-gray-50 rounded-lg transition">
                            <i class="fas fa-home mr-3 text-gray-400"></i>
                            <span>Website Homepage</span>
                            <i class="fas fa-external-link-alt ml-auto text-xs text-gray-400"></i>
                        </a>
                        <button onclick="refreshDashboard()" class="flex items-center p-2 text-gray-700 hover:bg-gray-50 rounded-lg transition w-full text-left">
                            <i class="fas fa-sync-alt mr-3 text-gray-400"></i>
                            <span>Refresh Dashboard</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Job Modal -->
    <div id="jobModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Post New Job</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <form id="jobForm" class="p-6 space-y-6">
                <!-- Company Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-4">Company Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                            <input type="text" name="company" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Website</label>
                            <input type="url" name="companyWebsite" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Industry *</label>
                            <select name="industry" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Industry</option>
                                <option value="technology">Technology</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="education">Education</option>
                                <option value="retail">Retail</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Size</label>
                            <select name="companySize" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Size</option>
                                <option value="1-10">1-10 employees</option>
                                <option value="11-50">11-50 employees</option>
                                <option value="51-200">51-200 employees</option>
                                <option value="201-500">201-500 employees</option>
                                <option value="500+">500+ employees</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Job Details -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-4">Job Details</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Job Title *</label>
                            <input type="text" name="title" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Job Category *</label>
                                <select id="categorySelect" name="category" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Category</option>
                                    <option value="software-development">Software Development</option>
                                    <option value="data-science">Data Science</option>
                                    <option value="design">Design</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="sales">Sales</option>
                                    <option value="hr">Human Resources</option>
                                    <option value="finance">Finance</option>
                                    <option value="operations">Operations</option>
                                    <option value="other">Other</option>
                                </select>
                                
                                <!-- Custom Category Field (Hidden by default) -->
                                <div id="customCategoryField" class="hidden mt-3">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Custom Category *</label>
                                    <input type="text" id="customCategory" name="customCategory" placeholder="Enter custom category" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Employment Type *</label>
                                <select name="type" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Type</option>
                                    <option value="full-time">Full-time</option>
                                    <option value="part-time">Part-time</option>
                                    <option value="contract">Contract</option>
                                    <option value="freelance">Freelance</option>
                                    <option value="internship">Internship</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Experience Level *</label>
                                <select name="experience" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Level</option>
                                    <option value="entry">Entry Level (0-2 years)</option>
                                    <option value="mid">Mid Level (3-5 years)</option>
                                    <option value="senior">Senior Level (6-10 years)</option>
                                    <option value="lead">Lead/Principal (10+ years)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Work Location *</label>
                                <select name="workLocation" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Location</option>
                                    <option value="remote">Remote</option>
                                    <option value="hybrid">Hybrid</option>
                                    <option value="onsite">On-site</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Location/City *</label>
                            <input type="text" name="location" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Salary (USD)</label>
                                <input type="text" name="salary" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Enter salary amount">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Salary Period</label>
                                <select name="salaryPeriod" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="yearly">Per Year</option>
                                    <option value="monthly">Per Month</option>
                                    <option value="hourly">Per Hour</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Job Description *</label>
                            <textarea name="description" required rows="6" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Describe the job role, responsibilities, and requirements..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Skills (comma-separated)</label>
                            <input type="text" name="skills" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. JavaScript, React, Node.js, MongoDB">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Application Email</label>
                            <input type="email" name="applicationEmail" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Optional: For email applications">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Application URL *</label>
                            <input type="url" name="applicationUrl" required class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://company.com/careers/job-id">
                            <p class="text-sm text-gray-600 mt-1">Direct link to company's job application page</p>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" id="cancelBtn" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                        Cancel
                    </button>
                    <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-save mr-2"></i>
                        <span id="submitBtnText">Post Job</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module">
        import { firebaseAuth, firebaseJobs, FirebaseUtils } from './js/firebase-config.js';

        let currentEditingJobId = null;

        // Check authentication on page load
        firebaseAuth.addAuthStateListener((user) => {
            if (!user || !firebaseAuth.isAdminAuthenticated()) {
                window.location.href = 'admin-login.html';
                return;
            }
            
            // User is authenticated, initialize dashboard
            initializeDashboard(user);
        });

        // Initialize dashboard
        async function initializeDashboard(user) {
            try {
                // Update admin info
                document.getElementById('adminEmail').textContent = user.email;
                
                // Load dashboard data
                await loadDashboardStats();
                await loadJobsList();
                
                // Hide loading screen
                document.getElementById('loadingScreen').classList.add('hidden');
                
            } catch (error) {
                console.error('Error initializing dashboard:', error);
                FirebaseUtils.showNotification('Error loading dashboard data', 'error');
            }
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                const stats = await firebaseJobs.getJobStats();
                
                document.getElementById('totalJobs').textContent = stats.total;
                document.getElementById('activeJobs').textContent = stats.active;
                document.getElementById('totalViews').textContent = stats.totalViews.toLocaleString();
                document.getElementById('totalApplications').textContent = stats.totalApplications.toLocaleString();
                
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load jobs list
        async function loadJobsList() {
            try {
                const jobs = await firebaseJobs.getJobs({ status: null }); // Get all jobs
                const jobsList = document.getElementById('jobsList');
                const emptyState = document.getElementById('emptyJobsState');
                
                if (jobs.length === 0) {
                    jobsList.innerHTML = '';
                    emptyState.classList.remove('hidden');
                    return;
                }
                
                emptyState.classList.add('hidden');
                
                jobsList.innerHTML = jobs.map(job => `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h3 class="font-semibold text-gray-900">${job.title}</h3>
                                    <span class="px-2 py-1 text-xs rounded-full ${
                                        job.status === 'active' ? 'bg-green-100 text-green-800' :
                                        job.status === 'inactive' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                    }">${job.status}</span>
                                </div>
                                <p class="text-gray-600 text-sm mb-2">${job.company} • ${job.location}</p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span><i class="fas fa-eye mr-1"></i>${job.views || 0} views</span>
                                    <span><i class="fas fa-file-alt mr-1"></i>${job.applications || 0} applications</span>
                                    <span><i class="fas fa-calendar mr-1"></i>${FirebaseUtils.formatDate(job.createdAt)}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <button onclick="editJob('${job.id}')" class="text-blue-600 hover:text-blue-800 p-1" title="Edit Job">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="toggleJobStatus('${job.id}', '${job.status}')" class="text-yellow-600 hover:text-yellow-800 p-1" title="Toggle Status">
                                    <i class="fas fa-toggle-${job.status === 'active' ? 'on' : 'off'}"></i>
                                </button>
                                <button onclick="deleteJob('${job.id}')" class="text-red-600 hover:text-red-800 p-1" title="Delete Job">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('Error loading jobs:', error);
                FirebaseUtils.showNotification('Error loading jobs list', 'error');
            }
        }

        // Modal functions
        function openJobModal(job = null) {
            const modal = document.getElementById('jobModal');
            const form = document.getElementById('jobForm');
            const title = document.getElementById('modalTitle');
            const submitBtn = document.getElementById('submitBtnText');
            
            if (job) {
                // Edit mode
                currentEditingJobId = job.id;
                title.textContent = 'Edit Job';
                submitBtn.textContent = 'Update Job';
                
                // Populate form with job data
                Object.keys(job).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        if (key === 'skills' && Array.isArray(job[key])) {
                            input.value = job[key].join(', ');
                        } else if (key === 'salary') {
                            // Handle salary prefilling based on salaryMin and salaryMax
                            if (job.salaryMin && job.salaryMax) {
                                if (job.salaryMin !== job.salaryMax) {
                                    input.value = `${job.salaryMin}-${job.salaryMax}`;
                                } else {
                                    input.value = job.salaryMin;
                                }
                            } else {
                                input.value = job[key] || '';
                            }
                        } else if (key === 'salaryMin' || key === 'salaryMax') {
                            // Skip these as they're handled in salary processing
                            return;
                        } else if (key === 'category') {
                            // Handle category - check if it's a custom category
                            const predefinedCategories = ['software-development', 'data-science', 'design', 'marketing', 'sales', 'hr', 'finance', 'operations'];
                            if (predefinedCategories.includes(job[key])) {
                                input.value = job[key];
                            } else {
                                // Custom category
                                input.value = 'other';
                                const customCategoryField = document.getElementById('customCategoryField');
                                const customCategoryInput = document.getElementById('customCategory');
                                customCategoryField.classList.remove('hidden');
                                customCategoryInput.required = true;
                                customCategoryInput.value = job[key];
                            }
                        } else {
                            input.value = job[key] || '';
                        }
                    }
                });

                // Debug log to check job data being loaded for editing
                console.log('Job data being loaded for editing:', job);
            } else {
                // Add mode
                currentEditingJobId = null;
                title.textContent = 'Post New Job';
                submitBtn.textContent = 'Post Job';
                form.reset();
                
                // Hide custom category field
                const customCategoryField = document.getElementById('customCategoryField');
                const customCategoryInput = document.getElementById('customCategory');
                customCategoryField.classList.add('hidden');
                customCategoryInput.required = false;
                customCategoryInput.value = '';
            }
            
            modal.classList.remove('hidden');
        }

        function closeJobModal() {
            document.getElementById('jobModal').classList.add('hidden');
            currentEditingJobId = null;
        }

        // Event listeners
        document.getElementById('addJobBtn').addEventListener('click', () => openJobModal());
        document.getElementById('closeModal').addEventListener('click', closeJobModal);
        document.getElementById('cancelBtn').addEventListener('click', closeJobModal);

        // Category selection handler
        document.getElementById('categorySelect').addEventListener('change', function() {
            const customCategoryField = document.getElementById('customCategoryField');
            const customCategoryInput = document.getElementById('customCategory');
            
            if (this.value === 'other') {
                customCategoryField.classList.remove('hidden');
                customCategoryInput.required = true;
            } else {
                customCategoryField.classList.add('hidden');
                customCategoryInput.required = false;
                customCategoryInput.value = '';
            }
        });

        // Job form submission
        document.getElementById('jobForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const jobData = {};
            
            // Convert form data to object
            for (let [key, value] of formData.entries()) {
                if (key === 'skills') {
                    jobData[key] = value.split(',').map(skill => skill.trim()).filter(skill => skill);
                } else if (key === 'salary') {
                    // Handle salary range or single value
                    if (!value || value.trim() === '') {
                        jobData.salary = '';
                        jobData.salaryMin = '';
                        jobData.salaryMax = '';
                    } else if (value.includes('-')) {
                        const parts = value.split('-');
                        if (parts.length === 2) {
                            jobData.salaryMin = parts[0].trim();
                            jobData.salaryMax = parts[1].trim();
                            jobData.salary = value.trim();
                        } else {
                            jobData.salary = value.trim();
                            jobData.salaryMin = value.trim();
                            jobData.salaryMax = value.trim();
                        }
                    } else {
                        jobData.salary = value.trim();
                        jobData.salaryMin = value.trim();
                        jobData.salaryMax = value.trim();
                    }
                } else if (key === 'category') {
                    // Handle custom category
                    if (value === 'other') {
                        const customCategory = formData.get('customCategory');
                        jobData[key] = customCategory || 'other';
                    } else {
                        jobData[key] = value;
                    }
                } else if (key === 'customCategory') {
                    // Skip customCategory as it's handled in category processing
                    continue;
                } else {
                    jobData[key] = value;
                }
            }

            // Debug log to check salary data
            console.log('Job data being saved:', jobData);
            
            try {
                if (currentEditingJobId) {
                    // When updating existing job, remove old salary fields to avoid confusion
                    if (jobData.salary) {
                        jobData.salaryMin = null;
                        jobData.salaryMax = null;
                    }

                    // Update existing job
                    await firebaseJobs.updateJob(currentEditingJobId, jobData);
                    FirebaseUtils.showNotification('Job updated successfully!', 'success');
                } else {
                    // Add new job
                    await firebaseJobs.addJob(jobData);
                    FirebaseUtils.showNotification('Job posted successfully!', 'success');
                }
                
                closeJobModal();
                await loadJobsList();
                await loadDashboardStats();
                
            } catch (error) {
                console.error('Error saving job:', error);
                FirebaseUtils.showNotification('Error saving job. Please try again.', 'error');
            }
        });

        // Global functions for job actions
        window.editJob = async function(jobId) {
            try {
                const jobs = await firebaseJobs.getJobs({ status: null });
                const job = jobs.find(j => j.id === jobId);
                if (job) {
                    openJobModal(job);
                }
            } catch (error) {
                console.error('Error loading job for edit:', error);
                FirebaseUtils.showNotification('Error loading job data', 'error');
            }
        };

        window.toggleJobStatus = async function(jobId, currentStatus) {
            try {
                const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
                await firebaseJobs.updateJob(jobId, { status: newStatus });
                FirebaseUtils.showNotification(`Job ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully!`, 'success');
                await loadJobsList();
                await loadDashboardStats();
            } catch (error) {
                console.error('Error toggling job status:', error);
                FirebaseUtils.showNotification('Error updating job status', 'error');
            }
        };

        window.deleteJob = async function(jobId) {
            if (!confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
                return;
            }
            
            try {
                await firebaseJobs.deleteJob(jobId);
                FirebaseUtils.showNotification('Job deleted successfully!', 'success');
                await loadJobsList();
                await loadDashboardStats();
            } catch (error) {
                console.error('Error deleting job:', error);
                FirebaseUtils.showNotification('Error deleting job', 'error');
            }
        };

        window.refreshDashboard = async function() {
            try {
                await loadDashboardStats();
                await loadJobsList();
                FirebaseUtils.showNotification('Dashboard refreshed!', 'success');
            } catch (error) {
                console.error('Error refreshing dashboard:', error);
                FirebaseUtils.showNotification('Error refreshing dashboard', 'error');
            }
        };

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', async function() {
            if (confirm('Are you sure you want to logout?')) {
                try {
                    await firebaseAuth.signOut();
                    window.location.href = 'admin-login.html';
                } catch (error) {
                    console.error('Logout error:', error);
                    FirebaseUtils.showNotification('Error logging out', 'error');
                }
            }
        });
    </script>
</body>
</html>