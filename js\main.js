// Main JavaScript file for USA EasyNaukri4U

// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            const icon = mobileMenuBtn.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars text-xl';
            } else {
                icon.className = 'fas fa-times text-xl';
            }
        });
    }

    // Initialize other components
    initializeBookmarks();

    // Only initialize notifications on dashboard pages
    if (document.querySelector('.notification-btn')) {
        initializeNotifications();
    }

    initializeSearch();
    initializeFilters();
    initializeCheckboxFilters();
});

// Bookmark functionality
function initializeBookmarks() {
    const bookmarkButtons = document.querySelectorAll('.bookmark-btn');

    bookmarkButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const icon = this.querySelector('i');
            const isBookmarked = icon.classList.contains('fas');
            
            if (isBookmarked) {
                icon.className = 'far fa-heart';
                this.classList.remove('text-danger');
                this.classList.add('text-gray-400');
                showNotification('Removed from bookmarks', 'info');
            } else {
                icon.className = 'fas fa-heart';
                this.classList.remove('text-gray-400');
                this.classList.add('text-danger');
                showNotification('Added to bookmarks', 'success');
            }
        });
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-20 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
    
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';
    
    notification.className += ` ${bgColor} text-white`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Notification dropdown
function initializeNotifications() {
    const notificationBtn = document.querySelector('.notification-btn');
    const notificationDropdown = document.querySelector('.notification-dropdown');
    
    if (notificationBtn && notificationDropdown) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            notificationDropdown.classList.add('hidden');
        });
    }
}

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    const searchButtons = document.querySelectorAll('.search-btn');

    searchInputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    });

    searchButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            performSearch();
        });
    });
}

function performSearch() {
    const jobTitle = document.querySelector('input[placeholder*="Job title"]')?.value || '';
    const location = document.querySelector('input[placeholder*="City"]')?.value || '';
    
    if (!jobTitle && !location) {
        showNotification('Please enter a job title or location', 'warning');
        return;
    }
    
    // Perform search - ready for API integration
    showNotification('Searching for jobs...', 'info');
    
    // Redirect to jobs page with search parameters
    const params = new URLSearchParams();
    if (jobTitle) params.append('q', jobTitle);
    if (location) params.append('location', location);
    
    setTimeout(() => {
        window.location.href = `jobs.html?${params.toString()}`;
    }, 1000);
}

// Filter functionality
function initializeFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const quickFilters = document.querySelectorAll('.quick-filter');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.dataset.filter;
            toggleFilter(filterType, this);
        });
    });
    
    quickFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const filterValue = this.textContent.trim();
            applyQuickFilter(filterValue);
        });
    });
}

function toggleFilter(filterType, button) {
    button.classList.toggle('active');
    const isActive = button.classList.contains('active');
    
    if (isActive) {
        button.classList.add('bg-primary', 'text-white');
        button.classList.remove('bg-gray-100', 'text-gray-700');
    } else {
        button.classList.remove('bg-primary', 'text-white');
        button.classList.add('bg-gray-100', 'text-gray-700');
    }
    
    // Apply filter logic here
    applyFilters();
}

function applyQuickFilter(filterValue) {
    // Add visual feedback
    const quickFilters = document.querySelectorAll('.quick-filter');
    quickFilters.forEach(filter => {
        filter.classList.remove('bg-primary', 'text-white');
        filter.classList.add('bg-blue-50', 'text-primary');
    });
    
    event.target.classList.add('bg-primary', 'text-white');
    event.target.classList.remove('bg-blue-50', 'text-primary');
    
    showNotification(`Filtering by: ${filterValue}`, 'info');
}

function applyFilters() {
    const activeFilters = document.querySelectorAll('.filter-btn.active');
    const filterValues = Array.from(activeFilters).map(btn => btn.dataset.filter);

    // Filter job listings functionality
    console.log('Active filters:', filterValues);
}

// Checkbox filter functionality for jobs page
function initializeCheckboxFilters() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    const clearFiltersBtn = document.querySelector('.clear-filters-btn');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const filterText = this.parentNode.querySelector('span').textContent;

            if (this.checked) {
                showNotification(`Filter applied: ${filterText}`, 'info');
            } else {
                showNotification(`Filter removed: ${filterText}`, 'info');
            }

            // Apply filters
            applyCheckboxFilters();
        });
    });

    // Clear all filters
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            showNotification('All filters cleared', 'info');
            applyCheckboxFilters();
        });
    }
}

function applyCheckboxFilters() {
    const checkedFilters = document.querySelectorAll('input[type="checkbox"]:checked');
    const filterValues = Array.from(checkedFilters).map(checkbox => {
        return checkbox.parentNode.querySelector('span').textContent;
    });

    // Apply checkbox filters to job listings
    console.log('Active checkbox filters:', filterValues);

    // Update job listings display (placeholder)
    updateJobListings(filterValues);
}

function updateJobListings(filters) {
    // Update job listings based on selected filters
    const jobCards = document.querySelectorAll('.job-card');

    if (filters.length === 0) {
        // Show all jobs
        jobCards.forEach(card => {
            card.style.display = 'block';
        });
    } else {
        // Apply filters to job cards
        jobCards.forEach(card => {
            card.style.display = 'block'; // Show all jobs for now - can be enhanced with specific filtering logic
        });
    }
}

// Progress bar animation
function animateProgressBar(element, targetPercentage) {
    const progressBar = element.querySelector('.progress-fill');
    const progressText = element.querySelector('.progress-text');
    
    let currentPercentage = 0;
    const increment = targetPercentage / 50; // 50 steps for smooth animation
    
    const timer = setInterval(() => {
        currentPercentage += increment;
        
        if (currentPercentage >= targetPercentage) {
            currentPercentage = targetPercentage;
            clearInterval(timer);
        }
        
        progressBar.style.width = `${currentPercentage}%`;
        if (progressText) {
            progressText.textContent = `${Math.round(currentPercentage)}%`;
        }
    }, 20);
}

// Form validation
function validateForm(formElement) {
    const requiredFields = formElement.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        const value = field.value.trim();
        const errorElement = field.parentNode.querySelector('.error-message');
        
        if (!value) {
            isValid = false;
            field.classList.add('border-red-500');
            if (errorElement) {
                errorElement.textContent = 'This field is required';
                errorElement.classList.remove('hidden');
            }
        } else {
            field.classList.remove('border-red-500');
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
        }
    });
    
    return isValid;
}

// File upload handling
function handleFileUpload(inputElement, allowedTypes = []) {
    inputElement.addEventListener('change', function(e) {
        const file = e.target.files[0];
        
        if (!file) return;
        
        // Check file type
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            showNotification('Invalid file type', 'error');
            inputElement.value = '';
            return;
        }
        
        // Check file size (5MB limit)
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            showNotification('File size must be less than 5MB', 'error');
            inputElement.value = '';
            return;
        }
        
        showNotification('File uploaded successfully', 'success');
        
        // Update UI to show selected file
        const fileName = file.name;
        const fileDisplay = inputElement.parentNode.querySelector('.file-display');
        if (fileDisplay) {
            fileDisplay.textContent = fileName;
            fileDisplay.classList.remove('hidden');
        }
    });
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date));
}

function timeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
    
    const intervals = {
        year: 31536000,
        month: 2592000,
        week: 604800,
        day: 86400,
        hour: 3600,
        minute: 60
    };
    
    for (const [unit, seconds] of Object.entries(intervals)) {
        const interval = Math.floor(diffInSeconds / seconds);
        if (interval >= 1) {
            return `${interval} ${unit}${interval > 1 ? 's' : ''} ago`;
        }
    }
    
    return 'Just now';
}

// Local storage helpers
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error reading from localStorage:', error);
        return null;
    }
}

// Tab functionality
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => {
                btn.classList.remove('border-primary', 'text-primary');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // Add active class to clicked button
            this.classList.add('border-primary', 'text-primary');
            this.classList.remove('border-transparent', 'text-gray-500');

            // Show target content
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }
        });
    });
}

// Modal functionality
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Initialize modals
function initializeModals() {
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-backdrop')) {
            const modal = e.target;
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });

    // Close modal with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal-backdrop:not(.hidden)');
            if (openModal) {
                openModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }
    });
}

// Dashboard Navigation
function initializeDashboardNavigation() {
    const navLinks = document.querySelectorAll('.dashboard-nav-link');
    const sections = document.querySelectorAll('.dashboard-section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);

            // Remove active class from all links
            navLinks.forEach(l => {
                l.classList.remove('active', 'text-primary', 'bg-blue-50');
                l.classList.add('text-gray-700');
            });

            // Add active class to clicked link
            this.classList.add('active', 'text-primary', 'bg-blue-50');
            this.classList.remove('text-gray-700');

            // Hide all sections
            sections.forEach(section => {
                section.classList.add('hidden');
            });

            // Show target section
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }
        });
    });
}

// Export functions for use in other files
window.EasyNaukri = {
    showNotification,
    animateProgressBar,
    validateForm,
    handleFileUpload,
    formatCurrency,
    formatDate,
    timeAgo,
    saveToLocalStorage,
    getFromLocalStorage,
    initializeTabs,
    openModal,
    closeModal,
    initializeModals,
    initializeDashboardNavigation,
    initializeSearch,
    initializeBookmarks,
    initializeCheckboxFilters,
    initializeFilters
};
