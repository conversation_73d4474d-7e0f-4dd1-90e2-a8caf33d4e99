<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-primary font-medium">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <!-- For Employers dropdown removed for security -->
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold mb-4">Help Center</h1>
            <p class="text-xl opacity-90">Find answers to your questions</p>
        </div>
    </div>



    <!-- Content -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Contact Form -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Contact Support</h2>
            <p class="text-gray-600 text-center mb-8">Need help? Send us a message and we'll get back to you as soon as possible.</p>

            <form id="contact-form" class="max-w-2xl mx-auto">
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                        <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                    <select id="subject" name="subject" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Select a topic</option>
                        <option value="job-seeker">Job Seeker Support</option>
                        <option value="employer">Employer Support</option>
                        <option value="technical">Technical Issue</option>
                        <option value="account">Account Problem</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                    <textarea id="message" name="message" rows="6" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Please describe your issue or question in detail..."></textarea>
                </div>

                <div class="text-center">
                    <button type="submit" class="bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary-dark transition font-medium">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Message
                    </button>
                </div>
            </form>
        </div>

        <!-- FAQ Sections -->
        <div class="space-y-8">
            <!-- Job Seekers FAQ -->
            <div id="job-seekers" class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-user text-blue-600 mr-3"></i>
                    Job Seekers FAQ
                </h2>
                <div class="space-y-4">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I create a profile?</h3>
                        <p class="text-gray-700">Click "Sign Up" in the top right corner, choose "Job Seeker", and fill out your information. Make sure to upload your resume and complete your profile for better job matches.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I search for jobs?</h3>
                        <p class="text-gray-700">Use the "Find Jobs" page to search by keywords, location, job type, and salary range. You can also set up job alerts to get notified of new opportunities.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I apply for a job?</h3>
                        <p class="text-gray-700">Click "Apply Now" on any job listing. Fill out the application form with your details and submit. Your application will be sent directly to the employer.</p>
                    </div>
                    <div class="pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I track my applications?</h3>
                        <p class="text-gray-700">Yes! Log into your candidate dashboard to view all your applications, their status, and any responses from employers.</p>
                    </div>
                </div>
            </div>

            <!-- Employers FAQ -->
            <div id="employers" class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-building text-green-600 mr-3"></i>
                    Employers FAQ
                </h2>
                <div class="space-y-4">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I post a job?</h3>
                        <p class="text-gray-700">Sign up as an employer, access your dashboard, and click "Post New Job". Fill out the job details, requirements, and publish your listing.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I manage applications?</h3>
                        <p class="text-gray-700">In your employer dashboard, go to the "Applications" section to view, filter, and manage all applications for your job postings.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I edit my job postings?</h3>
                        <p class="text-gray-700">Yes, you can edit active job postings from your dashboard. Go to "Job Postings" and click the edit button on any listing.</p>
                    </div>
                    <div class="pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I contact candidates?</h3>
                        <p class="text-gray-700">You can contact candidates directly through the application management system or use the contact information provided in their applications.</p>
                    </div>
                </div>
            </div>

            <!-- Account FAQ -->
            <div id="account" class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-cog text-purple-600 mr-3"></i>
                    Account & Settings FAQ
                </h2>
                <div class="space-y-4">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I reset my password?</h3>
                        <p class="text-gray-700">Click "Forgot Password" on the login page and enter your email address. You'll receive instructions to reset your password.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I update my profile?</h3>
                        <p class="text-gray-700">Log into your dashboard and go to the profile or settings section. You can update your personal information, resume, and preferences.</p>
                    </div>
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I delete my account?</h3>
                        <p class="text-gray-700">Contact our support <NAME_EMAIL> to request account deletion. Please note this action is permanent.</p>
                    </div>
                    <div class="pb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">How do I manage email notifications?</h3>
                        <p class="text-gray-700">In your dashboard settings, you can customize which email notifications you receive, including job alerts and application updates.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg p-8 mt-12 text-center">
            <h2 class="text-2xl font-bold mb-4">Still Need Help?</h2>
            <p class="text-xl opacity-90 mb-6">Our support team is here to help you</p>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <i class="fas fa-envelope text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-1">Email Support</h3>
                    <p class="opacity-90"><EMAIL></p>
                </div>
                <div>
                    <i class="fas fa-clock text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-1">Business Hours</h3>
                    <p class="opacity-90">Mon-Fri 9AM-6PM EST</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <!-- <li><a href="login.html" class="hover:text-white transition">Sign In</a></li> -->
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // Help Center Functionality
        document.addEventListener('DOMContentLoaded', function() {

            // Contact form submission
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(this);
                    const name = formData.get('name');
                    const email = formData.get('email');
                    const subject = formData.get('subject');
                    const message = formData.get('message');

                    // Simple validation
                    if (!name || !email || !subject || !message) {
                        alert('Please fill in all required fields.');
                        return;
                    }

                    // Simulate form submission
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
                    submitButton.disabled = true;

                    // Simulate API call
                    setTimeout(() => {
                        alert('Thank you for your message! We will get back to you within 24 hours.');
                        this.reset();
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    }, 2000);
                });
            }
        });
    </script>
</body>
</html>
