<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Candidate Dashboard - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Dashboard Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                        <span class="text-sm text-gray-500 ml-2">| Candidate Dashboard</span>
                    </div>
                </div>

                <!-- Dashboard Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#dashboard" class="text-primary font-medium hover:text-secondary transition">
                        <i class="fas fa-tachometer-alt mr-1"></i>Dashboard
                    </a>
                    <a href="#jobs" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-search mr-1"></i>Find Jobs
                    </a>
                    <a href="#applications" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-file-alt mr-1"></i>Applications
                    </a>
                    <a href="#profile" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-user mr-1"></i>Profile
                    </a>
                </div>

                <!-- User Profile -->
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Notification Bell -->
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary transition relative notification-btn">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">5</span>
                        </button>
                        <!-- Notification Dropdown -->
                        <div class="notification-dropdown hidden absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
                            <div class="p-4 border-b">
                                <h3 class="font-semibold text-gray-900">Notifications</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-briefcase text-blue-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">New job match found: Senior Developer</p>
                                            <p class="text-xs text-gray-500">2 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Application submitted successfully</p>
                                            <p class="text-xs text-gray-500">1 hour ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-star text-yellow-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Profile viewed by Google Inc.</p>
                                            <p class="text-xs text-gray-500">3 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-calendar text-purple-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Interview scheduled for tomorrow</p>
                                            <p class="text-xs text-gray-500">5 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Application status updated</p>
                                            <p class="text-xs text-gray-500">1 day ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 border-t">
                                <a href="#" class="text-primary text-sm hover:underline">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Info Display -->
                    <div id="userInfo" class="hidden md:block">
                        <!-- User info will be populated by Firebase authentication -->
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary transition">
                            <div class="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="font-medium">Account</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">My Profile</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Settings</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Help</a>
                            <div class="border-t"></div>
                            <a href="../index.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-home mr-2"></i>Back to Website
                            </a>
                            <button id="logoutBtn" class="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="#dashboard" class="block py-2 text-primary font-medium">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <a href="#jobs" class="block py-2 text-gray-700">
                    <i class="fas fa-search mr-2"></i>Find Jobs
                </a>
                <a href="#applications" class="block py-2 text-gray-700">
                    <i class="fas fa-file-alt mr-2"></i>Applications
                </a>
                <a href="#profile" class="block py-2 text-gray-700">
                    <i class="fas fa-user mr-2"></i>Profile
                </a>
                <div class="border-t pt-2 mt-2">
                    <a href="#" class="block py-2 text-gray-700">My Profile</a>
                    <a href="../index.html" class="block py-2 text-gray-700">Back to Website</a>
                    <a href="../admin-login.html" class="block py-2 text-red-600 w-full text-left">Sign Out</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Dashboard Container -->
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-8">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-12 h-12 rounded-full">
                    <div>
                        <h3 class="font-semibold text-gray-900">John Doe</h3>
                        <p class="text-sm text-gray-600">Software Engineer</p>
                    </div>
                </div>

                <nav class="space-y-2">
                    <a href="#overview" class="dashboard-nav-link active flex items-center space-x-3 px-4 py-3 text-primary bg-blue-50 rounded-lg">
                        <i class="fas fa-chart-pie"></i>
                        <span>Overview</span>
                    </a>
                    <a href="#applications" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-file-alt"></i>
                        <span>My Applications</span>
                    </a>
                    <a href="#saved-jobs" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-bookmark"></i>
                        <span>Saved Jobs</span>
                    </a>
                    <a href="#profile" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-user"></i>
                        <span>My Profile</span>
                    </a>
                    <a href="#resume" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-file-pdf"></i>
                        <span>Resume</span>
                    </a>
                    <a href="#interviews" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-calendar-check"></i>
                        <span>Interviews</span>
                    </a>
                    <a href="#settings" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Overview Section -->
            <div id="overview" class="dashboard-section">
                <!-- Header -->
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Welcome back, John!</h1>
                        <p class="text-gray-600 mt-1">Here's your job search progress and recent activity.</p>
                    </div>
                    <button class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-search mr-2"></i>Find Jobs
                    </button>
                </div>

                <!-- Stats Cards -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Applications Sent</p>
                                <p class="text-3xl font-bold text-gray-900">24</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+3 this week
                                </p>
                            </div>
                            <div class="bg-blue-100 text-blue-600 p-3 rounded-lg">
                                <i class="fas fa-paper-plane text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Profile Views</p>
                                <p class="text-3xl font-bold text-gray-900">156</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+12% this month
                                </p>
                            </div>
                            <div class="bg-green-100 text-green-600 p-3 rounded-lg">
                                <i class="fas fa-eye text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Interview Invites</p>
                                <p class="text-3xl font-bold text-gray-900">7</p>
                                <p class="text-blue-600 text-sm mt-1">
                                    <i class="fas fa-calendar mr-1"></i>2 upcoming
                                </p>
                            </div>
                            <div class="bg-purple-100 text-purple-600 p-3 rounded-lg">
                                <i class="fas fa-handshake text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Saved Jobs</p>
                                <p class="text-3xl font-bold text-gray-900">18</p>
                                <p class="text-yellow-600 text-sm mt-1">
                                    <i class="fas fa-bookmark mr-1"></i>5 new matches
                                </p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-600 p-3 rounded-lg">
                                <i class="fas fa-star text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Applications & Recommended Jobs -->
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Recent Applications -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Recent Applications</h3>
                                <a href="#applications" class="text-primary hover:underline text-sm">View All</a>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Company" class="w-10 h-10 rounded-lg">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Senior Developer</h4>
                                        <p class="text-gray-600 text-sm">TechCorp Inc. • Applied 2 days ago</p>
                                    </div>
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">Under Review</span>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Company" class="w-10 h-10 rounded-lg">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Full Stack Engineer</h4>
                                        <p class="text-gray-600 text-sm">StartupXYZ • Applied 5 days ago</p>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Interview</span>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Company" class="w-10 h-10 rounded-lg">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Software Engineer</h4>
                                        <p class="text-gray-600 text-sm">MegaCorp • Applied 1 week ago</p>
                                    </div>
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">Rejected</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recommended Jobs -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Recommended Jobs</h3>
                                <a href="../jobs.html" class="text-primary hover:underline text-sm">View All</a>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">React Developer</h4>
                                        <button class="text-gray-400 hover:text-primary">
                                            <i class="fas fa-bookmark"></i>
                                        </button>
                                    </div>
                                    <p class="text-gray-600 text-sm mb-2">InnovateTech • San Francisco, CA</p>
                                    <p class="text-primary font-medium text-sm">$90k - $120k</p>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">95% Match</span>
                                        <button class="text-primary hover:underline text-sm">Apply Now</button>
                                    </div>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">Frontend Engineer</h4>
                                        <button class="text-gray-400 hover:text-primary">
                                            <i class="fas fa-bookmark"></i>
                                        </button>
                                    </div>
                                    <p class="text-gray-600 text-sm mb-2">DesignCo • Remote</p>
                                    <p class="text-primary font-medium text-sm">$85k - $110k</p>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">88% Match</span>
                                        <button class="text-primary hover:underline text-sm">Apply Now</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button class="bg-primary text-white p-4 rounded-lg hover:bg-secondary transition text-center">
                            <i class="fas fa-search text-2xl mb-2"></i>
                            <p class="font-medium">Search Jobs</p>
                        </button>
                        <button class="bg-green-500 text-white p-4 rounded-lg hover:bg-green-600 transition text-center">
                            <i class="fas fa-file-pdf text-2xl mb-2"></i>
                            <p class="font-medium">Update Resume</p>
                        </button>
                        <button class="bg-purple-500 text-white p-4 rounded-lg hover:bg-purple-600 transition text-center">
                            <i class="fas fa-user-edit text-2xl mb-2"></i>
                            <p class="font-medium">Edit Profile</p>
                        </button>
                        <button class="bg-orange-500 text-white p-4 rounded-lg hover:bg-orange-600 transition text-center">
                            <i class="fas fa-book text-2xl mb-2"></i>
                            <p class="font-medium">Study Materials</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Applications Section -->
            <div id="applications" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">My Applications</h2>
                    <div class="flex space-x-2">
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option>All Status</option>
                            <option>Applied</option>
                            <option>Under Review</option>
                            <option>Interview</option>
                            <option>Rejected</option>
                        </select>
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option>Last 30 days</option>
                            <option>Last 7 days</option>
                            <option>Last 3 months</option>
                        </select>
                    </div>
                </div>

                <!-- Applications List -->
                <div class="space-y-4">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Google" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Senior Software Engineer</h3>
                                    <p class="text-gray-600">Google Inc. • Mountain View, CA</p>
                                    <p class="text-sm text-gray-500">Applied 3 days ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Under Review</span>
                                <p class="text-sm text-gray-500 mt-1">$150,000/year</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-users mr-1"></i>45 applicants</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Details</button>
                                <button class="text-red-600 hover:underline text-sm">Withdraw</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Microsoft" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Product Manager</h3>
                                    <p class="text-gray-600">Microsoft • Seattle, WA</p>
                                    <p class="text-sm text-gray-500">Applied 1 week ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Interview Scheduled</span>
                                <p class="text-sm text-gray-500 mt-1">$130,000/year</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-calendar mr-1"></i>Interview: Dec 15, 2:00 PM</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Details</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm">Join Interview</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Meta" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Frontend Developer</h3>
                                    <p class="text-gray-600">Meta • Menlo Park, CA</p>
                                    <p class="text-sm text-gray-500">Applied 2 weeks ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Not Selected</span>
                                <p class="text-sm text-gray-500 mt-1">$125,000/year</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-users mr-1"></i>89 applicants</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Feedback</button>
                                <button class="text-gray-600 hover:underline text-sm">Archive</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center mt-8">
                    <nav class="flex space-x-2">
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
                        <button class="px-3 py-2 bg-primary text-white rounded">1</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">2</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">3</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
                    </nav>
                </div>
            </div>

            <!-- Saved Jobs Section -->
            <div id="saved-jobs" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Saved Jobs</h2>
                    <div class="flex space-x-2">
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option>All Categories</option>
                            <option>Software Engineering</option>
                            <option>Product Management</option>
                            <option>Design</option>
                        </select>
                        <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-secondary transition">
                            <i class="fas fa-search mr-1"></i>Find More Jobs
                        </button>
                    </div>
                </div>

                <!-- Saved Jobs Grid -->
                <div class="grid lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Netflix" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">DevOps Engineer</h3>
                                    <p class="text-gray-600">Netflix • Los Gatos, CA</p>
                                    <p class="text-primary font-semibold">$140,000/year</p>
                                </div>
                            </div>
                            <button class="text-red-500 hover:text-red-700">
                                <i class="fas fa-heart text-xl"></i>
                            </button>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">AWS</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Docker</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Kubernetes</span>
                            </div>
                            <p class="text-gray-600 text-sm">Help us scale our infrastructure to serve millions of users worldwide...</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-calendar mr-1"></i>Posted 2 days ago</span>
                            </div>
                            <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-secondary transition">
                                Apply Now
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Apple" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">UX Designer</h3>
                                    <p class="text-gray-600">Apple • Cupertino, CA</p>
                                    <p class="text-primary font-semibold">$115,000/year</p>
                                </div>
                            </div>
                            <button class="text-red-500 hover:text-red-700">
                                <i class="fas fa-heart text-xl"></i>
                            </button>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Figma</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Sketch</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Prototyping</span>
                            </div>
                            <p class="text-gray-600 text-sm">Design intuitive and beautiful user experiences for our consumer products...</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-calendar mr-1"></i>Posted 1 week ago</span>
                            </div>
                            <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-secondary transition">
                                Apply Now
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Spotify" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Backend Developer</h3>
                                    <p class="text-gray-600">Spotify • New York, NY</p>
                                    <p class="text-primary font-semibold">$135,000/year</p>
                                </div>
                            </div>
                            <button class="text-red-500 hover:text-red-700">
                                <i class="fas fa-heart text-xl"></i>
                            </button>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Java</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Spring Boot</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">PostgreSQL</span>
                            </div>
                            <p class="text-gray-600 text-sm">Build scalable backend services for our music streaming platform...</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-calendar mr-1"></i>Posted 3 days ago</span>
                            </div>
                            <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-secondary transition">
                                Apply Now
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Tesla" class="w-12 h-12 rounded-lg">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">ML Engineer</h3>
                                    <p class="text-gray-600">Tesla • Palo Alto, CA</p>
                                    <p class="text-primary font-semibold">$145,000/year</p>
                                </div>
                            </div>
                            <button class="text-red-500 hover:text-red-700">
                                <i class="fas fa-heart text-xl"></i>
                            </button>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">Python</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">TensorFlow</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">PyTorch</span>
                            </div>
                            <p class="text-gray-600 text-sm">Work on autonomous driving technology and AI systems...</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-clock mr-1"></i>Full-time</span>
                                <span><i class="fas fa-calendar mr-1"></i>Posted 5 days ago</span>
                            </div>
                            <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-secondary transition">
                                Apply Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Section -->
            <div id="profile" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">My Profile</h2>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-edit mr-2"></i>Edit Profile
                    </button>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Profile Info -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Basic Information -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                    <input type="text" value="John Doe" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input type="tel" value="+****************" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                                    <input type="text" value="San Francisco, CA" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Summary -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Professional Summary</h3>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>Experienced Software Engineer with 5+ years of expertise in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable web applications and leading cross-functional teams.</textarea>
                        </div>

                        <!-- Experience -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Work Experience</h3>
                            <div class="space-y-6">
                                <div class="border-l-4 border-primary pl-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-semibold text-gray-900">Senior Software Engineer</h4>
                                        <span class="text-sm text-gray-600">2021 - Present</span>
                                    </div>
                                    <p class="text-primary font-medium mb-2">TechCorp Inc. • San Francisco, CA</p>
                                    <p class="text-gray-600 text-sm">Led development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%.</p>
                                </div>
                                <div class="border-l-4 border-gray-300 pl-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-semibold text-gray-900">Software Engineer</h4>
                                        <span class="text-sm text-gray-600">2019 - 2021</span>
                                    </div>
                                    <p class="text-primary font-medium mb-2">StartupXYZ • Palo Alto, CA</p>
                                    <p class="text-gray-600 text-sm">Developed full-stack web applications using React and Node.js. Collaborated with design team to implement responsive UI components.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Education -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Education</h3>
                            <div class="border-l-4 border-primary pl-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-semibold text-gray-900">Bachelor of Science in Computer Science</h4>
                                    <span class="text-sm text-gray-600">2015 - 2019</span>
                                </div>
                                <p class="text-primary font-medium mb-2">Stanford University • Stanford, CA</p>
                                <p class="text-gray-600 text-sm">GPA: 3.8/4.0 • Dean's List • Relevant Coursework: Data Structures, Algorithms, Software Engineering</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Profile Photo -->
                        <div class="bg-white rounded-lg shadow-md p-6 text-center">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Profile" class="w-24 h-24 rounded-full mx-auto mb-4">
                            <h3 class="font-semibold text-gray-900">John Doe</h3>
                            <p class="text-gray-600 text-sm">Software Engineer</p>
                            <button class="mt-3 text-primary hover:underline text-sm">Change Photo</button>
                        </div>

                        <!-- Skills -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Skills</h3>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">JavaScript</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">React</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Node.js</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Python</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">AWS</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Docker</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">MongoDB</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">PostgreSQL</span>
                            </div>
                            <button class="mt-3 text-primary hover:underline text-sm">+ Add Skill</button>
                        </div>

                        <!-- Certifications -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Certifications</h3>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3">
                                    <div class="bg-orange-100 text-orange-600 p-2 rounded">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">AWS Solutions Architect</p>
                                        <p class="text-gray-600 text-xs">Amazon Web Services • 2023</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">React Developer</p>
                                        <p class="text-gray-600 text-xs">Meta • 2022</p>
                                    </div>
                                </div>
                            </div>
                            <button class="mt-3 text-primary hover:underline text-sm">+ Add Certification</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resume Section -->
            <div id="resume" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Resume</h2>
                    <div class="flex space-x-2">
                        <button class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition">
                            <i class="fas fa-download mr-2"></i>Download PDF
                        </button>
                        <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                            <i class="fas fa-edit mr-2"></i>Edit Resume
                        </button>
                    </div>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Resume Preview -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-md p-8" style="min-height: 800px;">
                            <!-- Resume Header -->
                            <div class="text-center mb-8 pb-6 border-b border-gray-200">
                                <h1 class="text-3xl font-bold text-gray-900 mb-2">John Doe</h1>
                                <p class="text-lg text-gray-600 mb-3">Senior Software Engineer</p>
                                <div class="flex justify-center space-x-6 text-sm text-gray-600">
                                    <span><i class="fas fa-envelope mr-1"></i><EMAIL></span>
                                    <span><i class="fas fa-phone mr-1"></i>+****************</span>
                                    <span><i class="fas fa-map-marker-alt mr-1"></i>San Francisco, CA</span>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-3 border-b border-gray-200 pb-1">Professional Summary</h2>
                                <p class="text-gray-700 leading-relaxed">Experienced Software Engineer with 5+ years of expertise in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable web applications and leading cross-functional teams to achieve business objectives.</p>
                            </div>

                            <!-- Work Experience -->
                            <div class="mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-3 border-b border-gray-200 pb-1">Work Experience</h2>
                                <div class="space-y-4">
                                    <div>
                                        <div class="flex justify-between items-start mb-1">
                                            <h3 class="text-lg font-semibold text-gray-900">Senior Software Engineer</h3>
                                            <span class="text-sm text-gray-600">2021 - Present</span>
                                        </div>
                                        <p class="text-primary font-medium mb-2">TechCorp Inc. • San Francisco, CA</p>
                                        <ul class="text-gray-700 text-sm space-y-1 ml-4">
                                            <li>• Led development of microservices architecture serving 1M+ users</li>
                                            <li>• Implemented CI/CD pipelines reducing deployment time by 60%</li>
                                            <li>• Mentored 3 junior developers and conducted code reviews</li>
                                            <li>• Collaborated with product team to define technical requirements</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <div class="flex justify-between items-start mb-1">
                                            <h3 class="text-lg font-semibold text-gray-900">Software Engineer</h3>
                                            <span class="text-sm text-gray-600">2019 - 2021</span>
                                        </div>
                                        <p class="text-primary font-medium mb-2">StartupXYZ • Palo Alto, CA</p>
                                        <ul class="text-gray-700 text-sm space-y-1 ml-4">
                                            <li>• Developed full-stack web applications using React and Node.js</li>
                                            <li>• Collaborated with design team to implement responsive UI components</li>
                                            <li>• Optimized database queries improving application performance by 40%</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Education -->
                            <div class="mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-3 border-b border-gray-200 pb-1">Education</h2>
                                <div>
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="text-lg font-semibold text-gray-900">Bachelor of Science in Computer Science</h3>
                                        <span class="text-sm text-gray-600">2015 - 2019</span>
                                    </div>
                                    <p class="text-primary font-medium mb-1">Stanford University • Stanford, CA</p>
                                    <p class="text-gray-700 text-sm">GPA: 3.8/4.0 • Dean's List</p>
                                </div>
                            </div>

                            <!-- Skills -->
                            <div class="mb-6">
                                <h2 class="text-xl font-bold text-gray-900 mb-3 border-b border-gray-200 pb-1">Technical Skills</h2>
                                <div class="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
                                    <div>
                                        <p><strong>Languages:</strong> JavaScript, Python, TypeScript, Java</p>
                                        <p><strong>Frontend:</strong> React, Vue.js, HTML5, CSS3, Tailwind CSS</p>
                                    </div>
                                    <div>
                                        <p><strong>Backend:</strong> Node.js, Express, Django, Spring Boot</p>
                                        <p><strong>Cloud & Tools:</strong> AWS, Docker, Kubernetes, Git</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Certifications -->
                            <div>
                                <h2 class="text-xl font-bold text-gray-900 mb-3 border-b border-gray-200 pb-1">Certifications</h2>
                                <ul class="text-gray-700 text-sm space-y-1">
                                    <li>• AWS Solutions Architect Associate (2023)</li>
                                    <li>• Meta React Developer Certificate (2022)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Resume Tools -->
                    <div class="space-y-6">
                        <!-- Resume Stats -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Resume Analytics</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Profile Views</span>
                                    <span class="font-semibold text-gray-900">127</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Downloads</span>
                                    <span class="font-semibold text-gray-900">23</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Last Updated</span>
                                    <span class="font-semibold text-gray-900">2 days ago</span>
                                </div>
                            </div>
                        </div>

                        <!-- Resume Templates -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Templates</h3>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="border-2 border-primary rounded-lg p-3 text-center">
                                    <div class="bg-primary text-white p-2 rounded mb-2">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <p class="text-xs font-medium">Modern</p>
                                    <p class="text-xs text-green-600">Active</p>
                                </div>
                                <div class="border border-gray-300 rounded-lg p-3 text-center hover:border-primary cursor-pointer">
                                    <div class="bg-gray-400 text-white p-2 rounded mb-2">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <p class="text-xs font-medium">Classic</p>
                                </div>
                                <div class="border border-gray-300 rounded-lg p-3 text-center hover:border-primary cursor-pointer">
                                    <div class="bg-gray-400 text-white p-2 rounded mb-2">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <p class="text-xs font-medium">Creative</p>
                                </div>
                                <div class="border border-gray-300 rounded-lg p-3 text-center hover:border-primary cursor-pointer">
                                    <div class="bg-gray-400 text-white p-2 rounded mb-2">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <p class="text-xs font-medium">Minimal</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                <button class="w-full bg-blue-50 text-blue-700 p-3 rounded-lg hover:bg-blue-100 transition text-left">
                                    <i class="fas fa-magic mr-2"></i>AI Resume Review
                                </button>
                                <button class="w-full bg-green-50 text-green-700 p-3 rounded-lg hover:bg-green-100 transition text-left">
                                    <i class="fas fa-share mr-2"></i>Share Resume
                                </button>
                                <button class="w-full bg-purple-50 text-purple-700 p-3 rounded-lg hover:bg-purple-100 transition text-left">
                                    <i class="fas fa-copy mr-2"></i>Duplicate Resume
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interviews Section -->
            <div id="interviews" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Interviews</h2>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-calendar-plus mr-2"></i>Schedule Interview
                    </button>
                </div>

                <!-- Interview Calendar -->
                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Upcoming Interviews</h3>
                            <div class="space-y-4">
                                <div class="border border-green-200 bg-green-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="bg-green-500 text-white p-2 rounded-lg">
                                                <i class="fas fa-video"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900">Technical Interview - Microsoft</h4>
                                                <p class="text-gray-600 text-sm">Product Manager Position</p>
                                            </div>
                                        </div>
                                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Today</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex space-x-4 text-sm text-gray-600">
                                            <span><i class="fas fa-clock mr-1"></i>2:00 PM - 3:00 PM</span>
                                            <span><i class="fas fa-user mr-1"></i>Sarah Johnson</span>
                                        </div>
                                        <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 transition">
                                            Join Now
                                        </button>
                                    </div>
                                </div>

                                <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="bg-blue-500 text-white p-2 rounded-lg">
                                                <i class="fas fa-handshake"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900">Final Round - Google</h4>
                                                <p class="text-gray-600 text-sm">Senior Software Engineer</p>
                                            </div>
                                        </div>
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Tomorrow</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex space-x-4 text-sm text-gray-600">
                                            <span><i class="fas fa-clock mr-1"></i>10:00 AM - 11:30 AM</span>
                                            <span><i class="fas fa-user mr-1"></i>Alex Chen</span>
                                        </div>
                                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-600 transition">
                                            Prepare
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Past Interviews -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Interviews</h3>
                            <div class="space-y-4">
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="bg-gray-400 text-white p-2 rounded-lg">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900">Phone Screening - Apple</h4>
                                                <p class="text-gray-600 text-sm">UX Designer Position</p>
                                            </div>
                                        </div>
                                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Completed</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex space-x-4 text-sm text-gray-600">
                                            <span><i class="fas fa-calendar mr-1"></i>Dec 8, 2024</span>
                                            <span><i class="fas fa-star mr-1"></i>Positive Feedback</span>
                                        </div>
                                        <button class="text-primary hover:underline text-sm">View Feedback</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Interview Sidebar -->
                    <div class="space-y-6">
                        <!-- Interview Prep -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Interview Prep</h3>
                            <div class="space-y-3">
                                <button class="w-full bg-blue-50 text-blue-700 p-3 rounded-lg hover:bg-blue-100 transition text-left">
                                    <i class="fas fa-book mr-2"></i>Study Materials
                                </button>
                                <button class="w-full bg-green-50 text-green-700 p-3 rounded-lg hover:bg-green-100 transition text-left">
                                    <i class="fas fa-question-circle mr-2"></i>Practice Questions
                                </button>
                                <button class="w-full bg-purple-50 text-purple-700 p-3 rounded-lg hover:bg-purple-100 transition text-left">
                                    <i class="fas fa-video mr-2"></i>Mock Interviews
                                </button>
                            </div>
                        </div>

                        <!-- Interview Stats -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Interview Stats</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Total Interviews</span>
                                    <span class="font-semibold text-gray-900">8</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Success Rate</span>
                                    <span class="font-semibold text-green-600">75%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Avg. Rating</span>
                                    <span class="font-semibold text-yellow-600">4.2/5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="dashboard-section hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Settings</h2>

                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Account Settings -->
                    <div class="space-y-6">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Settings</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                    <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                    <input type="tel" value="+****************" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                    <button class="text-primary hover:underline text-sm">Change Password</button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Privacy Settings</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Profile Visibility</p>
                                        <p class="text-sm text-gray-600">Make your profile visible to recruiters</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Show Salary Expectations</p>
                                        <p class="text-sm text-gray-600">Display your salary range to employers</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="space-y-6">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Email Notifications</p>
                                        <p class="text-sm text-gray-600">Receive job alerts and updates via email</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">SMS Notifications</p>
                                        <p class="text-sm text-gray-600">Get interview reminders via SMS</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Push Notifications</p>
                                        <p class="text-sm text-gray-600">Browser notifications for new opportunities</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Preferences</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Preferred Job Types</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                                            <span class="ml-2 text-gray-700">Full-time</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-gray-700">Part-time</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-gray-700">Contract</span>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Salary Range</label>
                                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option>$100,000 - $150,000</option>
                                        <option>$150,000 - $200,000</option>
                                        <option>$200,000+</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Actions</h3>
                            <div class="space-y-3">
                                <button class="w-full bg-blue-50 text-blue-700 p-3 rounded-lg hover:bg-blue-100 transition text-left">
                                    <i class="fas fa-download mr-2"></i>Export My Data
                                </button>
                                <button class="w-full bg-yellow-50 text-yellow-700 p-3 rounded-lg hover:bg-yellow-100 transition text-left">
                                    <i class="fas fa-pause mr-2"></i>Deactivate Account
                                </button>
                                <button class="w-full bg-red-50 text-red-700 p-3 rounded-lg hover:bg-red-100 transition text-left">
                                    <i class="fas fa-trash mr-2"></i>Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="mt-8 flex justify-end">
                    <button class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script type="module">
        // Import Firebase modules for authentication
        import { firebaseAuth } from '../js/firebase-config.js';

        // Protect this page - redirect to admin login if not authenticated
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication status
            firebaseAuth.addAuthStateListener((user) => {
                if (!firebaseAuth.isAdminAuthenticated()) {
                    // Not authenticated as admin, redirect to login
                    alert('This dashboard is now restricted to administrators only. Redirecting to secure login...');
                    window.location.href = '../admin-login.html';
                    return;
                }

                // User is authenticated, initialize dashboard
                EasyNaukri.initializeDashboardNavigation();
            });
        });
    </script>
</body>
</html>
